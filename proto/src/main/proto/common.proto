syntax = "proto3";

package zlim.common.v1;

option java_package = "com.zlim.common.proto";
option java_outer_classname = "CommonProto";
option java_multiple_files = true;
option kotlin_package = "com.zlim.common.proto";

import "google/protobuf/timestamp.proto";

// 通用响应结果
message Result {
  bool success = 1;
  string error_code = 2;
  string message = 3;
  map<string, string> details = 4;
  google.protobuf.Timestamp timestamp = 5;
}

// 分页请求
message PageRequest {
  int32 page = 1;      // 页码，从1开始
  int32 size = 2;      // 每页大小
  string sort = 3;     // 排序字段
  string order = 4;    // 排序方向：ASC/DESC
}

// 分页响应
message PageResponse {
  int32 page = 1;
  int32 size = 2;
  int64 total = 3;
  int32 pages = 4;
  bool has_next = 5;
  bool has_previous = 6;
}

// 用户基本信息
message UserInfo {
  int64 user_id = 1;
  string username = 2;
  string nickname = 3;
  string avatar = 4;
  UserStatus status = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

// 用户状态枚举
enum UserStatus {
  USER_STATUS_UNSPECIFIED = 0;
  USER_STATUS_ACTIVE = 1;
  USER_STATUS_INACTIVE = 2;
  USER_STATUS_BANNED = 3;
  USER_STATUS_DELETED = 4;
}

// 设备信息
message DeviceInfo {
  string device_id = 1;
  DeviceType device_type = 2;
  string device_model = 3;
  string os_version = 4;
  string app_version = 5;
  string push_token = 6;
  google.protobuf.Timestamp last_active = 7;
}

// 设备类型枚举
enum DeviceType {
  DEVICE_TYPE_UNSPECIFIED = 0;
  DEVICE_TYPE_IOS = 1;
  DEVICE_TYPE_ANDROID = 2;
  DEVICE_TYPE_WEB = 3;
  DEVICE_TYPE_DESKTOP = 4;
  DEVICE_TYPE_MINI_PROGRAM = 5;
}

// 地理位置信息
message Location {
  double latitude = 1;
  double longitude = 2;
  string address = 3;
  string city = 4;
  string country = 5;
}

// 文件信息
message FileInfo {
  string file_id = 1;
  string filename = 2;
  string content_type = 3;
  int64 size = 4;
  string url = 5;
  string thumbnail_url = 6;
  map<string, string> metadata = 7;
  google.protobuf.Timestamp created_at = 8;
}

// 错误码枚举
enum ErrorCode {
  ERROR_CODE_UNSPECIFIED = 0;
  
  // 通用错误 1000-1999
  ERROR_CODE_SUCCESS = 1000;
  ERROR_CODE_INVALID_PARAMETER = 1001;
  ERROR_CODE_UNAUTHORIZED = 1002;
  ERROR_CODE_FORBIDDEN = 1003;
  ERROR_CODE_NOT_FOUND = 1004;
  ERROR_CODE_CONFLICT = 1005;
  ERROR_CODE_INTERNAL_ERROR = 1006;
  ERROR_CODE_SERVICE_UNAVAILABLE = 1007;
  ERROR_CODE_RATE_LIMITED = 1008;
  
  // 认证错误 2000-2999
  ERROR_CODE_INVALID_TOKEN = 2001;
  ERROR_CODE_TOKEN_EXPIRED = 2002;
  ERROR_CODE_INVALID_CREDENTIALS = 2003;
  ERROR_CODE_ACCOUNT_LOCKED = 2004;
  ERROR_CODE_ACCOUNT_DISABLED = 2005;
  
  // 用户错误 3000-3999
  ERROR_CODE_USER_NOT_FOUND = 3001;
  ERROR_CODE_USER_ALREADY_EXISTS = 3002;
  ERROR_CODE_INVALID_USERNAME = 3003;
  ERROR_CODE_INVALID_PASSWORD = 3004;
  ERROR_CODE_INVALID_EMAIL = 3005;
  ERROR_CODE_INVALID_PHONE = 3006;
  
  // 消息错误 4000-4999
  ERROR_CODE_MESSAGE_NOT_FOUND = 4001;
  ERROR_CODE_MESSAGE_TOO_LONG = 4002;
  ERROR_CODE_INVALID_MESSAGE_TYPE = 4003;
  ERROR_CODE_MESSAGE_SEND_FAILED = 4004;
  
  // 社交错误 5000-5999
  ERROR_CODE_FRIEND_NOT_FOUND = 5001;
  ERROR_CODE_FRIEND_ALREADY_EXISTS = 5002;
  ERROR_CODE_GROUP_NOT_FOUND = 5003;
  ERROR_CODE_GROUP_FULL = 5004;
  ERROR_CODE_NOT_GROUP_MEMBER = 5005;
  ERROR_CODE_INSUFFICIENT_PERMISSION = 5006;
  
  // 媒体错误 6000-6999
  ERROR_CODE_FILE_TOO_LARGE = 6001;
  ERROR_CODE_INVALID_FILE_TYPE = 6002;
  ERROR_CODE_UPLOAD_FAILED = 6003;
  ERROR_CODE_FILE_NOT_FOUND = 6004;
}

// 国际化消息
message I18nMessage {
  string key = 1;
  map<string, string> params = 2;
  string default_message = 3;
}

// 审计日志
message AuditLog {
  string id = 1;
  int64 user_id = 2;
  string action = 3;
  string resource = 4;
  string resource_id = 5;
  map<string, string> details = 6;
  string ip_address = 7;
  string user_agent = 8;
  google.protobuf.Timestamp created_at = 9;
}
