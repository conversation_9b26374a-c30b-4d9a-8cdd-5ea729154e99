syntax = "proto3";

package zlim.gateway.v1;

option java_package = "com.zlim.gateway.proto";
option java_outer_classname = "GatewayProto";
option java_multiple_files = true;
option kotlin_package = "com.zlim.gateway.proto";

import "common.proto";
import "message.proto";
import "google/protobuf/timestamp.proto";

// 网关服务（用于服务间通信）
service GatewayService {
  // 推送消息到客户端
  rpc PushMessage(PushMessageRequest) returns (PushMessageResponse);
  
  // 批量推送消息
  rpc PushBatchMessages(PushBatchMessagesRequest) returns (PushBatchMessagesResponse);
  
  // 获取在线用户
  rpc GetOnlineUsers(GetOnlineUsersRequest) returns (GetOnlineUsersResponse);
  
  // 检查用户是否在线
  rpc IsUserOnline(IsUserOnlineRequest) returns (IsUserOnlineResponse);
  
  // 强制断开用户连接
  rpc DisconnectUser(DisconnectUserRequest) returns (DisconnectUserResponse);
  
  // 广播消息
  rpc BroadcastMessage(BroadcastMessageRequest) returns (BroadcastMessageResponse);
  
  // 获取连接统计
  rpc GetConnectionStats(GetConnectionStatsRequest) returns (GetConnectionStatsResponse);
}

// WebSocket消息封装（客户端与网关通信）
message Envelope {
  uint32 cmd = 1;           // 命令类型
  bytes payload = 2;        // 消息载荷
  uint32 seq = 3;           // 序列号
  string request_id = 4;    // 请求ID
  google.protobuf.Timestamp timestamp = 5;
}

// 推送消息到客户端请求
message PushMessageRequest {
  repeated int64 user_ids = 1;
  zlim.message.v1.Message message = 2;
  bool require_ack = 3;     // 是否需要确认
  int32 timeout = 4;        // 超时时间（秒）
}

// 推送消息到客户端响应
message PushMessageResponse {
  zlim.common.v1.Result result = 1;
  repeated UserPushResult results = 2;
}

// 批量推送消息请求
message PushBatchMessagesRequest {
  repeated PushMessageRequest requests = 1;
}

// 批量推送消息响应
message PushBatchMessagesResponse {
  zlim.common.v1.Result result = 1;
  repeated PushMessageResponse responses = 2;
}

// 获取在线用户请求
message GetOnlineUsersRequest {
  repeated int64 user_ids = 1; // 如果为空，返回所有在线用户
  bool include_details = 2;
}

// 获取在线用户响应
message GetOnlineUsersResponse {
  zlim.common.v1.Result result = 1;
  repeated OnlineUser online_users = 2;
  int64 total_count = 3;
}

// 检查用户是否在线请求
message IsUserOnlineRequest {
  int64 user_id = 1;
}

// 检查用户是否在线响应
message IsUserOnlineResponse {
  zlim.common.v1.Result result = 1;
  bool is_online = 2;
  OnlineUser user_info = 3;
}

// 强制断开用户连接请求
message DisconnectUserRequest {
  int64 user_id = 1;
  string reason = 2;
  bool all_devices = 3; // 是否断开所有设备
  string device_id = 4; // 特定设备ID
}

// 强制断开用户连接响应
message DisconnectUserResponse {
  zlim.common.v1.Result result = 1;
  int32 disconnected_count = 2;
}

// 广播消息请求
message BroadcastMessageRequest {
  BroadcastTarget target = 1;
  bytes message = 2;
  BroadcastType type = 3;
}

// 广播消息响应
message BroadcastMessageResponse {
  zlim.common.v1.Result result = 1;
  int64 target_count = 2;
  int64 sent_count = 3;
}

// 获取连接统计请求
message GetConnectionStatsRequest {
  bool include_details = 1;
}

// 获取连接统计响应
message GetConnectionStatsResponse {
  zlim.common.v1.Result result = 1;
  ConnectionStats stats = 2;
}

// 用户推送结果
message UserPushResult {
  int64 user_id = 1;
  bool success = 2;
  string error_message = 3;
  int32 device_count = 4; // 推送到的设备数量
}

// 在线用户信息
message OnlineUser {
  int64 user_id = 1;
  repeated DeviceConnection devices = 2;
  google.protobuf.Timestamp first_online = 3;
  google.protobuf.Timestamp last_active = 4;
}

// 设备连接信息
message DeviceConnection {
  string connection_id = 1;
  string device_id = 2;
  zlim.common.v1.DeviceType device_type = 3;
  string ip_address = 4;
  string user_agent = 5;
  google.protobuf.Timestamp connected_at = 6;
  google.protobuf.Timestamp last_heartbeat = 7;
  ConnectionStatus status = 8;
  string gateway_node = 9; // 网关节点ID
}

// 广播目标
message BroadcastTarget {
  oneof target {
    AllUsers all_users = 1;
    UserList user_list = 2;
    UserGroup user_group = 3;
    DeviceType device_type = 4;
  }
}

// 所有用户
message AllUsers {
  bool include_offline = 1;
}

// 用户列表
message UserList {
  repeated int64 user_ids = 1;
}

// 用户组
message UserGroup {
  string group_id = 1;
  string group_type = 2;
}

// 设备类型
message DeviceType {
  zlim.common.v1.DeviceType type = 1;
}

// 连接统计
message ConnectionStats {
  int64 total_connections = 1;
  int64 active_connections = 2;
  int64 idle_connections = 3;
  int64 total_users = 4;
  map<string, int64> connections_by_device_type = 5;
  map<string, int64> connections_by_gateway_node = 6;
  double average_connection_duration = 7; // 平均连接时长（秒）
  int64 messages_sent_per_second = 8;
  int64 messages_received_per_second = 9;
  google.protobuf.Timestamp stats_time = 10;
}

// WebSocket命令类型枚举
enum WebSocketCommand {
  WS_CMD_UNSPECIFIED = 0;
  
  // 连接管理
  WS_CMD_CONNECT = 1;
  WS_CMD_CONNECT_ACK = 2;
  WS_CMD_DISCONNECT = 3;
  WS_CMD_PING = 4;
  WS_CMD_PONG = 5;
  
  // 消息相关
  WS_CMD_MESSAGE_SEND = 10;
  WS_CMD_MESSAGE_ACK = 11;
  WS_CMD_MESSAGE_RECEIVE = 12;
  WS_CMD_MESSAGE_READ = 13;
  WS_CMD_MESSAGE_TYPING = 14;
  
  // 状态同步
  WS_CMD_STATUS_UPDATE = 20;
  WS_CMD_PRESENCE_UPDATE = 21;
  
  // 系统通知
  WS_CMD_SYSTEM_NOTIFICATION = 30;
  WS_CMD_ERROR = 31;
  
  // 自定义命令
  WS_CMD_CUSTOM = 100;
}

// 连接状态枚举
enum ConnectionStatus {
  CONNECTION_STATUS_UNSPECIFIED = 0;
  CONNECTION_STATUS_CONNECTING = 1;
  CONNECTION_STATUS_CONNECTED = 2;
  CONNECTION_STATUS_IDLE = 3;
  CONNECTION_STATUS_DISCONNECTING = 4;
  CONNECTION_STATUS_DISCONNECTED = 5;
  CONNECTION_STATUS_ERROR = 6;
}

// 广播类型枚举
enum BroadcastType {
  BROADCAST_TYPE_UNSPECIFIED = 0;
  BROADCAST_TYPE_SYSTEM_ANNOUNCEMENT = 1;
  BROADCAST_TYPE_MAINTENANCE_NOTICE = 2;
  BROADCAST_TYPE_FEATURE_UPDATE = 3;
  BROADCAST_TYPE_EMERGENCY_ALERT = 4;
  BROADCAST_TYPE_MARKETING = 5;
}

// 连接请求（客户端发送）
message ConnectRequest {
  string access_token = 1;
  zlim.common.v1.DeviceInfo device_info = 2;
  string client_version = 3;
  map<string, string> metadata = 4;
}

// 连接响应（服务端发送）
message ConnectResponse {
  bool success = 1;
  string error_code = 2;
  string error_message = 3;
  string connection_id = 4;
  int32 heartbeat_interval = 5; // 心跳间隔（秒）
  google.protobuf.Timestamp server_time = 6;
  map<string, string> server_info = 7;
}

// 心跳请求
message PingRequest {
  google.protobuf.Timestamp client_time = 1;
}

// 心跳响应
message PongResponse {
  google.protobuf.Timestamp server_time = 1;
  google.protobuf.Timestamp client_time = 2;
}

// 状态更新
message StatusUpdate {
  int64 user_id = 1;
  UserStatus status = 2;
  string status_message = 3;
  google.protobuf.Timestamp timestamp = 4;
}

// 在线状态枚举
enum UserStatus {
  USER_STATUS_UNSPECIFIED = 0;
  USER_STATUS_ONLINE = 1;
  USER_STATUS_AWAY = 2;
  USER_STATUS_BUSY = 3;
  USER_STATUS_INVISIBLE = 4;
  USER_STATUS_OFFLINE = 5;
}

// 错误响应
message ErrorResponse {
  string error_code = 1;
  string error_message = 2;
  map<string, string> details = 3;
  google.protobuf.Timestamp timestamp = 4;
}
