syntax = "proto3";

package zlim.social.v1;

option java_package = "com.zlim.social.proto";
option java_outer_classname = "SocialProto";
option java_multiple_files = true;
option kotlin_package = "com.zlim.social.proto";

import "common.proto";
import "google/protobuf/timestamp.proto";

// 社交服务
service SocialService {
  // 好友管理
  rpc SendFriendRequest(SendFriendRequestRequest) returns (SendFriendRequestResponse);
  rpc AcceptFriendRequest(AcceptFriendRequestRequest) returns (AcceptFriendRequestResponse);
  rpc RejectFriendRequest(RejectFriendRequestRequest) returns (RejectFriendRequestResponse);
  rpc RemoveFriend(RemoveFriendRequest) returns (RemoveFriendResponse);
  rpc GetFriends(GetFriendsRequest) returns (GetFriendsResponse);
  rpc GetFriendRequests(GetFriendRequestsRequest) returns (GetFriendRequestsResponse);
  rpc BlockUser(BlockUserRequest) returns (BlockUserResponse);
  rpc UnblockUser(UnblockUserRequest) returns (UnblockUserResponse);
  rpc GetBlockedUsers(GetBlockedUsersRequest) returns (GetBlockedUsersResponse);
  
  // 群组管理
  rpc CreateGroup(CreateGroupRequest) returns (CreateGroupResponse);
  rpc UpdateGroup(UpdateGroupRequest) returns (UpdateGroupResponse);
  rpc DeleteGroup(DeleteGroupRequest) returns (DeleteGroupResponse);
  rpc GetGroup(GetGroupRequest) returns (GetGroupResponse);
  rpc GetGroups(GetGroupsRequest) returns (GetGroupsResponse);
  rpc JoinGroup(JoinGroupRequest) returns (JoinGroupResponse);
  rpc LeaveGroup(LeaveGroupRequest) returns (LeaveGroupResponse);
  rpc InviteToGroup(InviteToGroupRequest) returns (InviteToGroupResponse);
  rpc RemoveFromGroup(RemoveFromGroupRequest) returns (RemoveFromGroupResponse);
  rpc GetGroupMembers(GetGroupMembersRequest) returns (GetGroupMembersResponse);
  rpc UpdateGroupMember(UpdateGroupMemberRequest) returns (UpdateGroupMemberResponse);
  rpc TransferGroupOwnership(TransferGroupOwnershipRequest) returns (TransferGroupOwnershipResponse);
  
  // 频道管理
  rpc CreateChannel(CreateChannelRequest) returns (CreateChannelResponse);
  rpc UpdateChannel(UpdateChannelRequest) returns (UpdateChannelResponse);
  rpc DeleteChannel(DeleteChannelRequest) returns (DeleteChannelResponse);
  rpc GetChannel(GetChannelRequest) returns (GetChannelResponse);
  rpc GetChannels(GetChannelsRequest) returns (GetChannelsResponse);
  rpc SubscribeChannel(SubscribeChannelRequest) returns (SubscribeChannelResponse);
  rpc UnsubscribeChannel(UnsubscribeChannelRequest) returns (UnsubscribeChannelResponse);
  rpc GetChannelSubscribers(GetChannelSubscribersRequest) returns (GetChannelSubscribersResponse);
}

// 发送好友请求
message SendFriendRequestRequest {
  int64 from_user_id = 1;
  int64 to_user_id = 2;
  string message = 3;
  string source = 4; // 来源：搜索、推荐、扫码等
}

message SendFriendRequestResponse {
  zlim.common.v1.Result result = 1;
  FriendRequest friend_request = 2;
}

// 接受好友请求
message AcceptFriendRequestRequest {
  int64 request_id = 1;
  int64 user_id = 2;
  string message = 3;
}

message AcceptFriendRequestResponse {
  zlim.common.v1.Result result = 1;
  Friendship friendship = 2;
}

// 拒绝好友请求
message RejectFriendRequestRequest {
  int64 request_id = 1;
  int64 user_id = 2;
  string reason = 3;
}

message RejectFriendRequestResponse {
  zlim.common.v1.Result result = 1;
}

// 删除好友
message RemoveFriendRequest {
  int64 user_id = 1;
  int64 friend_id = 2;
}

message RemoveFriendResponse {
  zlim.common.v1.Result result = 1;
}

// 获取好友列表
message GetFriendsRequest {
  int64 user_id = 1;
  zlim.common.v1.PageRequest page = 2;
  string search_query = 3;
  repeated string tags = 4;
}

message GetFriendsResponse {
  zlim.common.v1.Result result = 1;
  repeated Friendship friendships = 2;
  zlim.common.v1.PageResponse page = 3;
}

// 获取好友请求列表
message GetFriendRequestsRequest {
  int64 user_id = 1;
  FriendRequestDirection direction = 2; // 发送的或接收的
  FriendRequestStatus status = 3;
  zlim.common.v1.PageRequest page = 4;
}

message GetFriendRequestsResponse {
  zlim.common.v1.Result result = 1;
  repeated FriendRequest requests = 2;
  zlim.common.v1.PageResponse page = 3;
}

// 拉黑用户
message BlockUserRequest {
  int64 user_id = 1;
  int64 blocked_user_id = 2;
  string reason = 3;
}

message BlockUserResponse {
  zlim.common.v1.Result result = 1;
}

// 取消拉黑
message UnblockUserRequest {
  int64 user_id = 1;
  int64 blocked_user_id = 2;
}

message UnblockUserResponse {
  zlim.common.v1.Result result = 1;
}

// 获取黑名单
message GetBlockedUsersRequest {
  int64 user_id = 1;
  zlim.common.v1.PageRequest page = 2;
}

message GetBlockedUsersResponse {
  zlim.common.v1.Result result = 1;
  repeated BlockedUser blocked_users = 2;
  zlim.common.v1.PageResponse page = 3;
}

// 创建群组
message CreateGroupRequest {
  int64 creator_id = 1;
  string name = 2;
  string description = 3;
  string avatar = 4;
  GroupType type = 5;
  GroupJoinMode join_mode = 6;
  int32 max_members = 7;
  repeated int64 initial_member_ids = 8;
  map<string, string> settings = 9;
}

message CreateGroupResponse {
  zlim.common.v1.Result result = 1;
  Group group = 2;
}

// 更新群组
message UpdateGroupRequest {
  int64 group_id = 1;
  int64 operator_id = 2;
  optional string name = 3;
  optional string description = 4;
  optional string avatar = 5;
  optional GroupJoinMode join_mode = 6;
  optional int32 max_members = 7;
  map<string, string> settings = 8;
}

message UpdateGroupResponse {
  zlim.common.v1.Result result = 1;
  Group group = 2;
}

// 删除群组
message DeleteGroupRequest {
  int64 group_id = 1;
  int64 operator_id = 2;
  string reason = 3;
}

message DeleteGroupResponse {
  zlim.common.v1.Result result = 1;
}

// 获取群组信息
message GetGroupRequest {
  int64 group_id = 1;
  int64 user_id = 2;
  bool include_members = 3;
}

message GetGroupResponse {
  zlim.common.v1.Result result = 1;
  Group group = 2;
  repeated GroupMember members = 3;
}

// 获取群组列表
message GetGroupsRequest {
  int64 user_id = 1;
  zlim.common.v1.PageRequest page = 2;
  GroupType type = 3;
  string search_query = 4;
}

message GetGroupsResponse {
  zlim.common.v1.Result result = 1;
  repeated Group groups = 2;
  zlim.common.v1.PageResponse page = 3;
}

// 加入群组
message JoinGroupRequest {
  int64 group_id = 1;
  int64 user_id = 2;
  string message = 3; // 申请消息
  string invite_code = 4; // 邀请码
}

message JoinGroupResponse {
  zlim.common.v1.Result result = 1;
  GroupMember member = 2;
}

// 离开群组
message LeaveGroupRequest {
  int64 group_id = 1;
  int64 user_id = 2;
}

message LeaveGroupResponse {
  zlim.common.v1.Result result = 1;
}

// 邀请加入群组
message InviteToGroupRequest {
  int64 group_id = 1;
  int64 inviter_id = 2;
  repeated int64 invitee_ids = 3;
  string message = 4;
}

message InviteToGroupResponse {
  zlim.common.v1.Result result = 1;
  repeated GroupInvitation invitations = 2;
}

// 移除群成员
message RemoveFromGroupRequest {
  int64 group_id = 1;
  int64 operator_id = 2;
  int64 member_id = 3;
  string reason = 4;
}

message RemoveFromGroupResponse {
  zlim.common.v1.Result result = 1;
}

// 获取群成员列表
message GetGroupMembersRequest {
  int64 group_id = 1;
  int64 user_id = 2;
  zlim.common.v1.PageRequest page = 3;
  GroupMemberRole role = 4; // 过滤角色
  string search_query = 5;
}

message GetGroupMembersResponse {
  zlim.common.v1.Result result = 1;
  repeated GroupMember members = 2;
  zlim.common.v1.PageResponse page = 3;
}

// 更新群成员
message UpdateGroupMemberRequest {
  int64 group_id = 1;
  int64 operator_id = 2;
  int64 member_id = 3;
  optional GroupMemberRole role = 4;
  optional string nickname = 5;
  optional bool muted = 6;
  optional google.protobuf.Timestamp mute_until = 7;
}

message UpdateGroupMemberResponse {
  zlim.common.v1.Result result = 1;
  GroupMember member = 2;
}

// 转让群主
message TransferGroupOwnershipRequest {
  int64 group_id = 1;
  int64 current_owner_id = 2;
  int64 new_owner_id = 3;
}

message TransferGroupOwnershipResponse {
  zlim.common.v1.Result result = 1;
}

// 创建频道
message CreateChannelRequest {
  int64 creator_id = 1;
  string name = 2;
  string description = 3;
  string avatar = 4;
  ChannelType type = 5;
  bool is_public = 6;
  map<string, string> settings = 7;
}

message CreateChannelResponse {
  zlim.common.v1.Result result = 1;
  Channel channel = 2;
}

// 更新频道
message UpdateChannelRequest {
  int64 channel_id = 1;
  int64 operator_id = 2;
  optional string name = 3;
  optional string description = 4;
  optional string avatar = 5;
  optional bool is_public = 6;
  map<string, string> settings = 7;
}

message UpdateChannelResponse {
  zlim.common.v1.Result result = 1;
  Channel channel = 2;
}

// 删除频道
message DeleteChannelRequest {
  int64 channel_id = 1;
  int64 operator_id = 2;
  string reason = 3;
}

message DeleteChannelResponse {
  zlim.common.v1.Result result = 1;
}

// 获取频道信息
message GetChannelRequest {
  int64 channel_id = 1;
  int64 user_id = 2;
}

message GetChannelResponse {
  zlim.common.v1.Result result = 1;
  Channel channel = 2;
  bool is_subscribed = 3;
}

// 获取频道列表
message GetChannelsRequest {
  int64 user_id = 1;
  zlim.common.v1.PageRequest page = 2;
  ChannelType type = 3;
  bool only_subscribed = 4;
  string search_query = 5;
}

message GetChannelsResponse {
  zlim.common.v1.Result result = 1;
  repeated Channel channels = 2;
  zlim.common.v1.PageResponse page = 3;
}

// 订阅频道
message SubscribeChannelRequest {
  int64 channel_id = 1;
  int64 user_id = 2;
}

message SubscribeChannelResponse {
  zlim.common.v1.Result result = 1;
  ChannelSubscription subscription = 2;
}

// 取消订阅频道
message UnsubscribeChannelRequest {
  int64 channel_id = 1;
  int64 user_id = 2;
}

message UnsubscribeChannelResponse {
  zlim.common.v1.Result result = 1;
}

// 获取频道订阅者
message GetChannelSubscribersRequest {
  int64 channel_id = 1;
  int64 user_id = 2;
  zlim.common.v1.PageRequest page = 3;
}

message GetChannelSubscribersResponse {
  zlim.common.v1.Result result = 1;
  repeated ChannelSubscription subscriptions = 2;
  zlim.common.v1.PageResponse page = 3;
}

// 好友请求
message FriendRequest {
  int64 id = 1;
  int64 from_user_id = 2;
  int64 to_user_id = 3;
  string message = 4;
  string source = 5;
  FriendRequestStatus status = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  zlim.common.v1.UserInfo from_user = 9;
  zlim.common.v1.UserInfo to_user = 10;
}

// 好友关系
message Friendship {
  int64 id = 1;
  int64 user_id = 2;
  int64 friend_id = 3;
  string nickname = 4; // 好友备注
  repeated string tags = 5; // 好友标签
  bool is_favorite = 6; // 是否特别关注
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  zlim.common.v1.UserInfo friend_info = 9;
}

// 黑名单用户
message BlockedUser {
  int64 id = 1;
  int64 user_id = 2;
  int64 blocked_user_id = 3;
  string reason = 4;
  google.protobuf.Timestamp created_at = 5;
  zlim.common.v1.UserInfo blocked_user_info = 6;
}

// 群组
message Group {
  int64 id = 1;
  string name = 2;
  string description = 3;
  string avatar = 4;
  GroupType type = 5;
  GroupJoinMode join_mode = 6;
  int32 max_members = 7;
  int32 member_count = 8;
  int64 owner_id = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
  bool is_active = 12;
  map<string, string> settings = 13;
  string invite_code = 14;
}

// 群成员
message GroupMember {
  int64 id = 1;
  int64 group_id = 2;
  int64 user_id = 3;
  GroupMemberRole role = 4;
  string nickname = 5; // 群昵称
  google.protobuf.Timestamp joined_at = 6;
  bool is_muted = 7;
  google.protobuf.Timestamp mute_until = 8;
  zlim.common.v1.UserInfo user_info = 9;
}

// 群邀请
message GroupInvitation {
  int64 id = 1;
  int64 group_id = 2;
  int64 inviter_id = 3;
  int64 invitee_id = 4;
  string message = 5;
  InvitationStatus status = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp expires_at = 8;
}

// 频道
message Channel {
  int64 id = 1;
  string name = 2;
  string description = 3;
  string avatar = 4;
  ChannelType type = 5;
  bool is_public = 6;
  int64 owner_id = 7;
  int64 subscriber_count = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  bool is_active = 11;
  map<string, string> settings = 12;
}

// 频道订阅
message ChannelSubscription {
  int64 id = 1;
  int64 channel_id = 2;
  int64 user_id = 3;
  google.protobuf.Timestamp subscribed_at = 4;
  bool notifications_enabled = 5;
  zlim.common.v1.UserInfo user_info = 6;
}

// 枚举定义
enum FriendRequestStatus {
  FRIEND_REQUEST_STATUS_UNSPECIFIED = 0;
  FRIEND_REQUEST_STATUS_PENDING = 1;
  FRIEND_REQUEST_STATUS_ACCEPTED = 2;
  FRIEND_REQUEST_STATUS_REJECTED = 3;
  FRIEND_REQUEST_STATUS_EXPIRED = 4;
}

enum FriendRequestDirection {
  FRIEND_REQUEST_DIRECTION_UNSPECIFIED = 0;
  FRIEND_REQUEST_DIRECTION_SENT = 1;
  FRIEND_REQUEST_DIRECTION_RECEIVED = 2;
}

enum GroupType {
  GROUP_TYPE_UNSPECIFIED = 0;
  GROUP_TYPE_PRIVATE = 1;  // 私有群
  GROUP_TYPE_PUBLIC = 2;   // 公开群
  GROUP_TYPE_OFFICIAL = 3; // 官方群
}

enum GroupJoinMode {
  GROUP_JOIN_MODE_UNSPECIFIED = 0;
  GROUP_JOIN_MODE_FREE = 1;        // 自由加入
  GROUP_JOIN_MODE_APPROVAL = 2;    // 需要审批
  GROUP_JOIN_MODE_INVITE_ONLY = 3; // 仅邀请
}

enum GroupMemberRole {
  GROUP_MEMBER_ROLE_UNSPECIFIED = 0;
  GROUP_MEMBER_ROLE_MEMBER = 1;
  GROUP_MEMBER_ROLE_ADMIN = 2;
  GROUP_MEMBER_ROLE_OWNER = 3;
}

enum InvitationStatus {
  INVITATION_STATUS_UNSPECIFIED = 0;
  INVITATION_STATUS_PENDING = 1;
  INVITATION_STATUS_ACCEPTED = 2;
  INVITATION_STATUS_REJECTED = 3;
  INVITATION_STATUS_EXPIRED = 4;
}

enum ChannelType {
  CHANNEL_TYPE_UNSPECIFIED = 0;
  CHANNEL_TYPE_NEWS = 1;        // 新闻频道
  CHANNEL_TYPE_ANNOUNCEMENT = 2; // 公告频道
  CHANNEL_TYPE_DISCUSSION = 3;   // 讨论频道
  CHANNEL_TYPE_ENTERTAINMENT = 4; // 娱乐频道
}
