syntax = "proto3";

package zlim.media.v1;

option java_package = "com.zlim.media.proto";
option java_outer_classname = "MediaProto";
option java_multiple_files = true;
option kotlin_package = "com.zlim.media.proto";

import "common.proto";
import "google/protobuf/timestamp.proto";

// 媒体服务
service MediaService {
  // 获取上传凭证
  rpc GetUploadCredentials(GetUploadCredentialsRequest) returns (GetUploadCredentialsResponse);
  
  // 确认上传完成
  rpc ConfirmUpload(ConfirmUploadRequest) returns (ConfirmUploadResponse);
  
  // 获取下载URL
  rpc GetDownloadUrl(GetDownloadUrlRequest) returns (GetDownloadUrlResponse);
  
  // 删除文件
  rpc DeleteFile(DeleteFileRequest) returns (DeleteFileResponse);
  
  // 获取文件信息
  rpc GetFileInfo(GetFileInfoRequest) returns (GetFileInfoResponse);
  
  // 批量获取文件信息
  rpc GetFilesInfo(GetFilesInfoRequest) returns (GetFilesInfoResponse);
  
  // 生成缩略图
  rpc GenerateThumbnail(GenerateThumbnailRequest) returns (GenerateThumbnailResponse);
  
  // 转换媒体格式
  rpc ConvertMedia(ConvertMediaRequest) returns (ConvertMediaResponse);
  
  // 获取媒体元数据
  rpc GetMediaMetadata(GetMediaMetadataRequest) returns (GetMediaMetadataResponse);
  
  // 内容审核
  rpc ModerateContent(ModerateContentRequest) returns (ModerateContentResponse);
}

// 获取上传凭证请求
message GetUploadCredentialsRequest {
  int64 user_id = 1;
  string filename = 2;
  string content_type = 3;
  int64 file_size = 4;
  MediaType media_type = 5;
  UploadPurpose purpose = 6;
  map<string, string> metadata = 7;
}

// 获取上传凭证响应
message GetUploadCredentialsResponse {
  zlim.common.v1.Result result = 1;
  UploadCredentials credentials = 2;
}

// 确认上传完成请求
message ConfirmUploadRequest {
  string upload_id = 1;
  int64 user_id = 2;
  string file_key = 3;
  string etag = 4;
  int64 actual_size = 5;
  string checksum = 6;
}

// 确认上传完成响应
message ConfirmUploadResponse {
  zlim.common.v1.Result result = 1;
  MediaFile file = 2;
}

// 获取下载URL请求
message GetDownloadUrlRequest {
  string file_id = 1;
  int64 user_id = 2;
  int32 expires_in = 3; // 过期时间（秒）
  bool thumbnail = 4;   // 是否获取缩略图URL
}

// 获取下载URL响应
message GetDownloadUrlResponse {
  zlim.common.v1.Result result = 1;
  string download_url = 2;
  google.protobuf.Timestamp expires_at = 3;
}

// 删除文件请求
message DeleteFileRequest {
  string file_id = 1;
  int64 user_id = 2;
  bool hard_delete = 3; // 是否物理删除
}

// 删除文件响应
message DeleteFileResponse {
  zlim.common.v1.Result result = 1;
}

// 获取文件信息请求
message GetFileInfoRequest {
  string file_id = 1;
  int64 user_id = 2;
  bool include_metadata = 3;
}

// 获取文件信息响应
message GetFileInfoResponse {
  zlim.common.v1.Result result = 1;
  MediaFile file = 2;
}

// 批量获取文件信息请求
message GetFilesInfoRequest {
  repeated string file_ids = 1;
  int64 user_id = 2;
  bool include_metadata = 3;
}

// 批量获取文件信息响应
message GetFilesInfoResponse {
  zlim.common.v1.Result result = 1;
  repeated MediaFile files = 2;
}

// 生成缩略图请求
message GenerateThumbnailRequest {
  string file_id = 1;
  ThumbnailSpec spec = 2;
  bool force_regenerate = 3;
}

// 生成缩略图响应
message GenerateThumbnailResponse {
  zlim.common.v1.Result result = 1;
  string thumbnail_url = 2;
}

// 转换媒体格式请求
message ConvertMediaRequest {
  string file_id = 1;
  MediaFormat target_format = 2;
  ConversionOptions options = 3;
}

// 转换媒体格式响应
message ConvertMediaResponse {
  zlim.common.v1.Result result = 1;
  string task_id = 2; // 异步任务ID
  MediaFile converted_file = 3; // 如果是同步转换
}

// 获取媒体元数据请求
message GetMediaMetadataRequest {
  string file_id = 1;
  bool extract_all = 2; // 是否提取所有元数据
}

// 获取媒体元数据响应
message GetMediaMetadataResponse {
  zlim.common.v1.Result result = 1;
  MediaMetadata metadata = 2;
}

// 内容审核请求
message ModerateContentRequest {
  string file_id = 1;
  repeated ModerationService services = 2;
  bool async_mode = 3;
}

// 内容审核响应
message ModerateContentResponse {
  zlim.common.v1.Result result = 1;
  ModerationResult moderation_result = 2;
  string task_id = 3; // 异步模式下的任务ID
}

// 上传凭证
message UploadCredentials {
  string upload_id = 1;
  string upload_url = 2;
  string file_key = 3;
  map<string, string> headers = 4;
  map<string, string> form_data = 5;
  google.protobuf.Timestamp expires_at = 6;
  int64 max_file_size = 7;
  repeated string allowed_content_types = 8;
}

// 媒体文件
message MediaFile {
  string file_id = 1;
  string filename = 2;
  string original_filename = 3;
  string content_type = 4;
  int64 file_size = 5;
  MediaType media_type = 6;
  UploadPurpose purpose = 7;
  int64 uploader_id = 8;
  string storage_path = 9;
  string public_url = 10;
  string thumbnail_url = 11;
  FileStatus status = 12;
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
  MediaMetadata metadata = 15;
  ModerationResult moderation_result = 16;
  map<string, string> custom_metadata = 17;
}

// 缩略图规格
message ThumbnailSpec {
  int32 width = 1;
  int32 height = 2;
  ThumbnailMode mode = 3; // 缩放模式
  string format = 4;      // 输出格式
  int32 quality = 5;      // 质量（1-100）
}

// 媒体格式
message MediaFormat {
  string format = 1;      // 格式名称：mp4, webp, jpg等
  string codec = 2;       // 编解码器
  int32 bitrate = 3;      // 比特率
  int32 width = 4;        // 宽度
  int32 height = 5;       // 高度
  int32 frame_rate = 6;   // 帧率
  int32 quality = 7;      // 质量
}

// 转换选项
message ConversionOptions {
  bool preserve_metadata = 1;
  bool optimize_for_web = 2;
  int32 max_width = 3;
  int32 max_height = 4;
  int32 max_bitrate = 5;
  map<string, string> custom_options = 6;
}

// 媒体元数据
message MediaMetadata {
  // 通用元数据
  int32 width = 1;
  int32 height = 2;
  int32 duration = 3;     // 时长（秒）
  int32 bitrate = 4;      // 比特率
  string format = 5;      // 格式
  string codec = 6;       // 编解码器
  
  // 图片元数据
  ImageMetadata image = 7;
  
  // 音频元数据
  AudioMetadata audio = 8;
  
  // 视频元数据
  VideoMetadata video = 9;
  
  // EXIF数据
  map<string, string> exif = 10;
  
  // 自定义元数据
  map<string, string> custom = 11;
}

// 图片元数据
message ImageMetadata {
  string color_space = 1;
  int32 dpi = 2;
  bool has_transparency = 3;
  int32 color_count = 4;
  repeated string dominant_colors = 5;
}

// 音频元数据
message AudioMetadata {
  string title = 1;
  string artist = 2;
  string album = 3;
  string genre = 4;
  int32 year = 5;
  int32 track_number = 6;
  int32 sample_rate = 7;
  int32 channels = 8;
}

// 视频元数据
message VideoMetadata {
  int32 frame_rate = 1;
  string aspect_ratio = 2;
  bool has_audio = 3;
  repeated VideoStream streams = 4;
}

// 视频流信息
message VideoStream {
  string codec = 1;
  int32 bitrate = 2;
  int32 width = 3;
  int32 height = 4;
  string language = 5;
}

// 审核结果
message ModerationResult {
  bool is_safe = 1;
  double confidence = 2;
  repeated ModerationLabel labels = 3;
  string reason = 4;
  google.protobuf.Timestamp moderated_at = 5;
  repeated ModerationService services_used = 6;
}

// 审核标签
message ModerationLabel {
  string category = 1;    // 类别：adult, violence, spam等
  string label = 2;       // 具体标签
  double confidence = 3;  // 置信度
  string description = 4; // 描述
}

// 枚举定义
enum MediaType {
  MEDIA_TYPE_UNSPECIFIED = 0;
  MEDIA_TYPE_IMAGE = 1;
  MEDIA_TYPE_AUDIO = 2;
  MEDIA_TYPE_VIDEO = 3;
  MEDIA_TYPE_DOCUMENT = 4;
  MEDIA_TYPE_ARCHIVE = 5;
  MEDIA_TYPE_OTHER = 6;
}

enum UploadPurpose {
  UPLOAD_PURPOSE_UNSPECIFIED = 0;
  UPLOAD_PURPOSE_AVATAR = 1;
  UPLOAD_PURPOSE_MESSAGE = 2;
  UPLOAD_PURPOSE_GROUP_AVATAR = 3;
  UPLOAD_PURPOSE_CHANNEL_AVATAR = 4;
  UPLOAD_PURPOSE_BACKGROUND = 5;
  UPLOAD_PURPOSE_ATTACHMENT = 6;
}

enum FileStatus {
  FILE_STATUS_UNSPECIFIED = 0;
  FILE_STATUS_UPLOADING = 1;
  FILE_STATUS_PROCESSING = 2;
  FILE_STATUS_AVAILABLE = 3;
  FILE_STATUS_FAILED = 4;
  FILE_STATUS_DELETED = 5;
  FILE_STATUS_QUARANTINED = 6; // 被隔离（审核不通过）
}

enum ThumbnailMode {
  THUMBNAIL_MODE_UNSPECIFIED = 0;
  THUMBNAIL_MODE_FIT = 1;        // 适应
  THUMBNAIL_MODE_FILL = 2;       // 填充
  THUMBNAIL_MODE_CROP = 3;       // 裁剪
  THUMBNAIL_MODE_STRETCH = 4;    // 拉伸
}

enum ModerationService {
  MODERATION_SERVICE_UNSPECIFIED = 0;
  MODERATION_SERVICE_CONTENT_SAFETY = 1;  // 内容安全
  MODERATION_SERVICE_ADULT_CONTENT = 2;   // 成人内容
  MODERATION_SERVICE_VIOLENCE = 3;        // 暴力内容
  MODERATION_SERVICE_SPAM = 4;            // 垃圾内容
  MODERATION_SERVICE_FACE_DETECTION = 5;  // 人脸检测
  MODERATION_SERVICE_TEXT_OCR = 6;        // 文字识别
}
