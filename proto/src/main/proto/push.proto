syntax = "proto3";

package zlim.push.v1;

option java_package = "com.zlim.push.proto";
option java_outer_classname = "PushProto";
option java_multiple_files = true;
option kotlin_package = "com.zlim.push.proto";

import "common.proto";
import "google/protobuf/timestamp.proto";

// 推送服务
service PushService {
  // 发送推送通知
  rpc SendPushNotification(SendPushNotificationRequest) returns (SendPushNotificationResponse);
  
  // 批量发送推送通知
  rpc SendBatchPushNotifications(SendBatchPushNotificationsRequest) returns (SendBatchPushNotificationsResponse);
  
  // 注册设备推送Token
  rpc RegisterDeviceToken(RegisterDeviceTokenRequest) returns (RegisterDeviceTokenResponse);
  
  // 注销设备推送Token
  rpc UnregisterDeviceToken(UnregisterDeviceTokenRequest) returns (UnregisterDeviceTokenResponse);
  
  // 获取推送统计
  rpc GetPushStats(GetPushStatsRequest) returns (GetPushStatsResponse);
  
  // 获取推送历史
  rpc GetPushHistory(GetPushHistoryRequest) returns (GetPushHistoryResponse);
  
  // 更新推送设置
  rpc UpdatePushSettings(UpdatePushSettingsRequest) returns (UpdatePushSettingsResponse);
  
  // 获取推送设置
  rpc GetPushSettings(GetPushSettingsRequest) returns (GetPushSettingsResponse);
  
  // 测试推送
  rpc TestPush(TestPushRequest) returns (TestPushResponse);
}

// 发送推送通知请求
message SendPushNotificationRequest {
  repeated int64 user_ids = 1;
  PushNotification notification = 2;
  PushOptions options = 3;
  string idempotency_key = 4; // 幂等性键
}

// 发送推送通知响应
message SendPushNotificationResponse {
  zlim.common.v1.Result result = 1;
  string push_id = 2;
  int32 target_count = 3; // 目标设备数量
  int32 sent_count = 4;   // 实际发送数量
}

// 批量发送推送通知请求
message SendBatchPushNotificationsRequest {
  repeated SendPushNotificationRequest notifications = 1;
}

// 批量发送推送通知响应
message SendBatchPushNotificationsResponse {
  zlim.common.v1.Result result = 1;
  repeated SendPushNotificationResponse results = 2;
}

// 注册设备推送Token请求
message RegisterDeviceTokenRequest {
  int64 user_id = 1;
  string device_id = 2;
  zlim.common.v1.DeviceType device_type = 3;
  string push_token = 4;
  PushProvider provider = 5;
  map<string, string> metadata = 6;
}

// 注册设备推送Token响应
message RegisterDeviceTokenResponse {
  zlim.common.v1.Result result = 1;
  DeviceToken device_token = 2;
}

// 注销设备推送Token请求
message UnregisterDeviceTokenRequest {
  int64 user_id = 1;
  string device_id = 2;
}

// 注销设备推送Token响应
message UnregisterDeviceTokenResponse {
  zlim.common.v1.Result result = 1;
}

// 获取推送统计请求
message GetPushStatsRequest {
  int64 user_id = 1; // 可选：特定用户的统计
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  PushProvider provider = 4; // 可选：特定推送提供商
}

// 获取推送统计响应
message GetPushStatsResponse {
  zlim.common.v1.Result result = 1;
  PushStats stats = 2;
}

// 获取推送历史请求
message GetPushHistoryRequest {
  int64 user_id = 1;
  zlim.common.v1.PageRequest page = 2;
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
  PushStatus status = 5; // 过滤状态
}

// 获取推送历史响应
message GetPushHistoryResponse {
  zlim.common.v1.Result result = 1;
  repeated PushRecord records = 2;
  zlim.common.v1.PageResponse page = 3;
}

// 更新推送设置请求
message UpdatePushSettingsRequest {
  int64 user_id = 1;
  PushSettings settings = 2;
}

// 更新推送设置响应
message UpdatePushSettingsResponse {
  zlim.common.v1.Result result = 1;
  PushSettings settings = 2;
}

// 获取推送设置请求
message GetPushSettingsRequest {
  int64 user_id = 1;
}

// 获取推送设置响应
message GetPushSettingsResponse {
  zlim.common.v1.Result result = 1;
  PushSettings settings = 2;
}

// 测试推送请求
message TestPushRequest {
  int64 user_id = 1;
  string device_id = 2;
  PushNotification notification = 3;
}

// 测试推送响应
message TestPushResponse {
  zlim.common.v1.Result result = 1;
  string push_id = 2;
  PushStatus status = 3;
}

// 推送通知
message PushNotification {
  string title = 1;
  string body = 2;
  string icon = 3;
  string image = 4;
  string sound = 5;
  int32 badge = 6;
  NotificationType type = 7;
  map<string, string> data = 8; // 自定义数据
  PushAction action = 9;
  repeated PushButton buttons = 10;
}

// 推送选项
message PushOptions {
  PushPriority priority = 1;
  google.protobuf.Timestamp schedule_time = 2; // 定时推送
  int32 ttl = 3; // 生存时间（秒）
  bool collapse_key = 4; // 是否合并相同类型的通知
  string collapse_id = 5; // 合并ID
  repeated PushProvider providers = 6; // 指定推送提供商
  bool silent = 7; // 静默推送
  map<string, string> custom_options = 8;
}

// 推送动作
message PushAction {
  string action_id = 1;
  string title = 2;
  string url = 3;
  map<string, string> parameters = 4;
}

// 推送按钮
message PushButton {
  string button_id = 1;
  string title = 2;
  string action = 3;
  string url = 4;
}

// 设备Token
message DeviceToken {
  string id = 1;
  int64 user_id = 2;
  string device_id = 3;
  zlim.common.v1.DeviceType device_type = 4;
  string push_token = 5;
  PushProvider provider = 6;
  bool is_active = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp last_used = 10;
  map<string, string> metadata = 11;
}

// 推送统计
message PushStats {
  int64 total_sent = 1;
  int64 total_delivered = 2;
  int64 total_clicked = 3;
  int64 total_failed = 4;
  double delivery_rate = 5;
  double click_rate = 6;
  map<string, int64> stats_by_provider = 7;
  map<string, int64> stats_by_type = 8;
  google.protobuf.Timestamp period_start = 9;
  google.protobuf.Timestamp period_end = 10;
}

// 推送记录
message PushRecord {
  string push_id = 1;
  int64 user_id = 2;
  string device_id = 3;
  PushNotification notification = 4;
  PushProvider provider = 5;
  PushStatus status = 6;
  string error_message = 7;
  google.protobuf.Timestamp sent_at = 8;
  google.protobuf.Timestamp delivered_at = 9;
  google.protobuf.Timestamp clicked_at = 10;
  map<string, string> metadata = 11;
}

// 推送设置
message PushSettings {
  bool enabled = 1;
  bool message_notifications = 2;
  bool friend_request_notifications = 3;
  bool group_notifications = 4;
  bool system_notifications = 5;
  bool sound_enabled = 6;
  bool vibration_enabled = 7;
  bool led_enabled = 8;
  string quiet_hours_start = 9; // HH:MM
  string quiet_hours_end = 10;  // HH:MM
  repeated int32 quiet_days = 11; // 0=Sunday, 1=Monday, ...
  map<string, bool> category_settings = 12; // 分类设置
}

// 枚举定义
enum PushProvider {
  PUSH_PROVIDER_UNSPECIFIED = 0;
  PUSH_PROVIDER_FCM = 1;        // Firebase Cloud Messaging
  PUSH_PROVIDER_APNS = 2;       // Apple Push Notification Service
  PUSH_PROVIDER_JPUSH = 3;      // 极光推送
  PUSH_PROVIDER_UMENG = 4;      // 友盟推送
  PUSH_PROVIDER_GETUI = 5;      // 个推
  PUSH_PROVIDER_HUAWEI = 6;     // 华为推送
  PUSH_PROVIDER_XIAOMI = 7;     // 小米推送
  PUSH_PROVIDER_OPPO = 8;       // OPPO推送
  PUSH_PROVIDER_VIVO = 9;       // VIVO推送
  PUSH_PROVIDER_MEIZU = 10;     // 魅族推送
}

enum NotificationType {
  NOTIFICATION_TYPE_UNSPECIFIED = 0;
  NOTIFICATION_TYPE_MESSAGE = 1;
  NOTIFICATION_TYPE_FRIEND_REQUEST = 2;
  NOTIFICATION_TYPE_GROUP_INVITE = 3;
  NOTIFICATION_TYPE_SYSTEM = 4;
  NOTIFICATION_TYPE_MARKETING = 5;
  NOTIFICATION_TYPE_REMINDER = 6;
  NOTIFICATION_TYPE_ALERT = 7;
}

enum PushPriority {
  PUSH_PRIORITY_UNSPECIFIED = 0;
  PUSH_PRIORITY_LOW = 1;
  PUSH_PRIORITY_NORMAL = 2;
  PUSH_PRIORITY_HIGH = 3;
  PUSH_PRIORITY_URGENT = 4;
}

enum PushStatus {
  PUSH_STATUS_UNSPECIFIED = 0;
  PUSH_STATUS_PENDING = 1;
  PUSH_STATUS_SENT = 2;
  PUSH_STATUS_DELIVERED = 3;
  PUSH_STATUS_CLICKED = 4;
  PUSH_STATUS_FAILED = 5;
  PUSH_STATUS_EXPIRED = 6;
}
