syntax = "proto3";

package zlim.message.v1;

option java_package = "com.zlim.message.proto";
option java_outer_classname = "MessageProto";
option java_multiple_files = true;
option kotlin_package = "com.zlim.message.proto";

import "common.proto";
import "google/protobuf/timestamp.proto";

// 消息服务
service MessageService {
  // 发送消息
  rpc SendMessage(SendMessageRequest) returns (SendMessageResponse);
  
  // 批量发送消息
  rpc SendBatchMessages(SendBatchMessagesRequest) returns (SendBatchMessagesResponse);
  
  // 获取消息历史
  rpc GetMessageHistory(GetMessageHistoryRequest) returns (GetMessageHistoryResponse);
  
  // 获取会话列表
  rpc GetConversations(GetConversationsRequest) returns (GetConversationsResponse);
  
  // 标记消息已读
  rpc MarkAsRead(MarkAsReadRequest) returns (MarkAsReadResponse);
  
  // 撤回消息
  rpc RecallMessage(RecallMessageRequest) returns (RecallMessageResponse);
  
  // 删除消息
  rpc DeleteMessage(DeleteMessageRequest) returns (DeleteMessageResponse);
  
  // 转发消息
  rpc ForwardMessage(ForwardMessageRequest) returns (ForwardMessageResponse);
  
  // 搜索消息
  rpc SearchMessages(SearchMessagesRequest) returns (SearchMessagesResponse);
  
  // 获取消息详情
  rpc GetMessage(GetMessageRequest) returns (GetMessageResponse);
  
  // 同步消息（用于多端同步）
  rpc SyncMessages(SyncMessagesRequest) returns (stream SyncMessagesResponse);
  
  // 获取未读消息数
  rpc GetUnreadCount(GetUnreadCountRequest) returns (GetUnreadCountResponse);
}

// 发送消息请求
message SendMessageRequest {
  string conversation_id = 1;
  MessageContent content = 2;
  MessageType type = 3;
  int64 sender_id = 4;
  repeated int64 recipient_ids = 5; // 私聊时只有一个，群聊时有多个
  string client_msg_id = 6; // 客户端消息ID，用于去重
  map<string, string> metadata = 7;
  MessagePriority priority = 8;
  google.protobuf.Timestamp schedule_time = 9; // 定时发送
}

// 发送消息响应
message SendMessageResponse {
  zlim.common.v1.Result result = 1;
  Message message = 2;
}

// 批量发送消息请求
message SendBatchMessagesRequest {
  repeated SendMessageRequest messages = 1;
}

// 批量发送消息响应
message SendBatchMessagesResponse {
  zlim.common.v1.Result result = 1;
  repeated Message messages = 2;
  repeated zlim.common.v1.Result results = 3; // 每条消息的发送结果
}

// 获取消息历史请求
message GetMessageHistoryRequest {
  string conversation_id = 1;
  int64 user_id = 2;
  zlim.common.v1.PageRequest page = 3;
  string cursor = 4; // 游标分页
  google.protobuf.Timestamp before = 5; // 获取此时间之前的消息
  google.protobuf.Timestamp after = 6;  // 获取此时间之后的消息
  repeated MessageType types = 7; // 过滤消息类型
}

// 获取消息历史响应
message GetMessageHistoryResponse {
  zlim.common.v1.Result result = 1;
  repeated Message messages = 2;
  zlim.common.v1.PageResponse page = 3;
  string next_cursor = 4;
  bool has_more = 5;
}

// 获取会话列表请求
message GetConversationsRequest {
  int64 user_id = 1;
  zlim.common.v1.PageRequest page = 2;
  ConversationType type = 3; // 过滤会话类型
  bool include_archived = 4;
}

// 获取会话列表响应
message GetConversationsResponse {
  zlim.common.v1.Result result = 1;
  repeated Conversation conversations = 2;
  zlim.common.v1.PageResponse page = 3;
}

// 标记已读请求
message MarkAsReadRequest {
  string conversation_id = 1;
  int64 user_id = 2;
  string message_id = 3; // 标记到此消息为止的所有消息已读
}

// 标记已读响应
message MarkAsReadResponse {
  zlim.common.v1.Result result = 1;
  int64 read_count = 2; // 标记已读的消息数量
}

// 撤回消息请求
message RecallMessageRequest {
  string message_id = 1;
  int64 user_id = 2;
  string reason = 3;
}

// 撤回消息响应
message RecallMessageResponse {
  zlim.common.v1.Result result = 1;
}

// 删除消息请求
message DeleteMessageRequest {
  string message_id = 1;
  int64 user_id = 2;
  bool delete_for_everyone = 3; // true: 删除所有人的消息, false: 只删除自己的
}

// 删除消息响应
message DeleteMessageResponse {
  zlim.common.v1.Result result = 1;
}

// 转发消息请求
message ForwardMessageRequest {
  string message_id = 1;
  repeated string target_conversation_ids = 2;
  int64 sender_id = 3;
  string comment = 4; // 转发时的评论
}

// 转发消息响应
message ForwardMessageResponse {
  zlim.common.v1.Result result = 1;
  repeated Message forwarded_messages = 2;
}

// 搜索消息请求
message SearchMessagesRequest {
  int64 user_id = 1;
  string query = 2;
  string conversation_id = 3; // 可选：在特定会话中搜索
  repeated MessageType types = 4;
  google.protobuf.Timestamp start_time = 5;
  google.protobuf.Timestamp end_time = 6;
  zlim.common.v1.PageRequest page = 7;
}

// 搜索消息响应
message SearchMessagesResponse {
  zlim.common.v1.Result result = 1;
  repeated Message messages = 2;
  zlim.common.v1.PageResponse page = 3;
  repeated string highlights = 4; // 高亮关键词
}

// 获取消息详情请求
message GetMessageRequest {
  string message_id = 1;
  int64 user_id = 2;
}

// 获取消息详情响应
message GetMessageResponse {
  zlim.common.v1.Result result = 1;
  Message message = 2;
}

// 同步消息请求
message SyncMessagesRequest {
  int64 user_id = 1;
  google.protobuf.Timestamp last_sync_time = 2;
  string device_id = 3;
}

// 同步消息响应
message SyncMessagesResponse {
  Message message = 1;
  SyncAction action = 2; // 同步动作：新增、更新、删除
  google.protobuf.Timestamp sync_time = 3;
}

// 获取未读消息数请求
message GetUnreadCountRequest {
  int64 user_id = 1;
  string conversation_id = 2; // 可选：获取特定会话的未读数
}

// 获取未读消息数响应
message GetUnreadCountResponse {
  zlim.common.v1.Result result = 1;
  int64 total_unread = 2;
  repeated ConversationUnread conversation_unreads = 3;
}

// 消息
message Message {
  string message_id = 1;
  string conversation_id = 2;
  int64 sender_id = 3;
  MessageContent content = 4;
  MessageType type = 5;
  MessageStatus status = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  google.protobuf.Timestamp delivered_at = 9;
  google.protobuf.Timestamp read_at = 10;
  repeated MessageReceipt receipts = 11; // 消息回执
  string client_msg_id = 12;
  map<string, string> metadata = 13;
  MessagePriority priority = 14;
  bool is_recalled = 15;
  bool is_deleted = 16;
  string reply_to_message_id = 17; // 回复的消息ID
  repeated string forward_from_message_ids = 18; // 转发的原消息ID
}

// 消息内容
message MessageContent {
  oneof content {
    TextContent text = 1;
    ImageContent image = 2;
    AudioContent audio = 3;
    VideoContent video = 4;
    FileContent file = 5;
    LocationContent location = 6;
    ContactContent contact = 7;
    LinkContent link = 8;
    SystemContent system = 9;
    CustomContent custom = 10;
  }
}

// 文本消息内容
message TextContent {
  string text = 1;
  repeated Mention mentions = 2; // @提及
  repeated string hashtags = 3;  // #话题
  repeated Emoji emojis = 4;     // 表情
}

// 图片消息内容
message ImageContent {
  string url = 1;
  string thumbnail_url = 2;
  int32 width = 3;
  int32 height = 4;
  int64 size = 5;
  string format = 6; // jpg, png, gif, webp
  string caption = 7; // 图片说明
}

// 音频消息内容
message AudioContent {
  string url = 1;
  int32 duration = 2; // 时长（秒）
  int64 size = 3;
  string format = 4; // mp3, aac, wav
  string waveform = 5; // 波形数据
}

// 视频消息内容
message VideoContent {
  string url = 1;
  string thumbnail_url = 2;
  int32 duration = 3; // 时长（秒）
  int32 width = 4;
  int32 height = 5;
  int64 size = 6;
  string format = 7; // mp4, mov, avi
  string caption = 8; // 视频说明
}

// 文件消息内容
message FileContent {
  string url = 1;
  string filename = 2;
  int64 size = 3;
  string mime_type = 4;
  string thumbnail_url = 5; // 文件预览图
}

// 位置消息内容
message LocationContent {
  double latitude = 1;
  double longitude = 2;
  string address = 3;
  string name = 4; // 地点名称
  string thumbnail_url = 5; // 地图缩略图
}

// 联系人消息内容
message ContactContent {
  string name = 1;
  string phone = 2;
  string email = 3;
  string avatar = 4;
  int64 user_id = 5; // 如果是系统内用户
}

// 链接消息内容
message LinkContent {
  string url = 1;
  string title = 2;
  string description = 3;
  string thumbnail_url = 4;
  string site_name = 5;
}

// 系统消息内容
message SystemContent {
  SystemMessageType type = 1;
  string text = 2;
  map<string, string> params = 3;
}

// 自定义消息内容
message CustomContent {
  string type = 1;
  bytes data = 2;
  map<string, string> properties = 3;
}

// 提及信息
message Mention {
  int64 user_id = 1;
  string username = 2;
  int32 start = 3; // 在文本中的起始位置
  int32 length = 4; // 长度
}

// 表情信息
message Emoji {
  string code = 1; // 表情代码
  string unicode = 2; // Unicode字符
  int32 start = 3; // 在文本中的起始位置
  int32 length = 4; // 长度
}

// 会话
message Conversation {
  string conversation_id = 1;
  ConversationType type = 2;
  string name = 3;
  string avatar = 4;
  string description = 5;
  repeated int64 participant_ids = 6;
  Message last_message = 7;
  int64 unread_count = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  bool is_muted = 11;
  bool is_pinned = 12;
  bool is_archived = 13;
  map<string, string> metadata = 14;
}

// 消息回执
message MessageReceipt {
  int64 user_id = 1;
  ReceiptType type = 2;
  google.protobuf.Timestamp timestamp = 3;
}

// 会话未读数
message ConversationUnread {
  string conversation_id = 1;
  int64 unread_count = 2;
  string last_message_id = 3;
}

// 消息类型枚举
enum MessageType {
  MESSAGE_TYPE_UNSPECIFIED = 0;
  MESSAGE_TYPE_TEXT = 1;
  MESSAGE_TYPE_IMAGE = 2;
  MESSAGE_TYPE_AUDIO = 3;
  MESSAGE_TYPE_VIDEO = 4;
  MESSAGE_TYPE_FILE = 5;
  MESSAGE_TYPE_LOCATION = 6;
  MESSAGE_TYPE_CONTACT = 7;
  MESSAGE_TYPE_LINK = 8;
  MESSAGE_TYPE_SYSTEM = 9;
  MESSAGE_TYPE_CUSTOM = 10;
}

// 消息状态枚举
enum MessageStatus {
  MESSAGE_STATUS_UNSPECIFIED = 0;
  MESSAGE_STATUS_SENDING = 1;
  MESSAGE_STATUS_SENT = 2;
  MESSAGE_STATUS_DELIVERED = 3;
  MESSAGE_STATUS_READ = 4;
  MESSAGE_STATUS_FAILED = 5;
}

// 消息优先级枚举
enum MessagePriority {
  MESSAGE_PRIORITY_UNSPECIFIED = 0;
  MESSAGE_PRIORITY_LOW = 1;
  MESSAGE_PRIORITY_NORMAL = 2;
  MESSAGE_PRIORITY_HIGH = 3;
  MESSAGE_PRIORITY_URGENT = 4;
}

// 会话类型枚举
enum ConversationType {
  CONVERSATION_TYPE_UNSPECIFIED = 0;
  CONVERSATION_TYPE_PRIVATE = 1; // 私聊
  CONVERSATION_TYPE_GROUP = 2;   // 群聊
  CONVERSATION_TYPE_CHANNEL = 3; // 频道
  CONVERSATION_TYPE_SYSTEM = 4;  // 系统会话
}

// 回执类型枚举
enum ReceiptType {
  RECEIPT_TYPE_UNSPECIFIED = 0;
  RECEIPT_TYPE_DELIVERED = 1;
  RECEIPT_TYPE_READ = 2;
}

// 同步动作枚举
enum SyncAction {
  SYNC_ACTION_UNSPECIFIED = 0;
  SYNC_ACTION_CREATE = 1;
  SYNC_ACTION_UPDATE = 2;
  SYNC_ACTION_DELETE = 3;
}

// 系统消息类型枚举
enum SystemMessageType {
  SYSTEM_MESSAGE_TYPE_UNSPECIFIED = 0;
  SYSTEM_MESSAGE_TYPE_USER_JOIN = 1;
  SYSTEM_MESSAGE_TYPE_USER_LEAVE = 2;
  SYSTEM_MESSAGE_TYPE_GROUP_CREATED = 3;
  SYSTEM_MESSAGE_TYPE_GROUP_RENAMED = 4;
  SYSTEM_MESSAGE_TYPE_GROUP_AVATAR_CHANGED = 5;
  SYSTEM_MESSAGE_TYPE_ADMIN_ADDED = 6;
  SYSTEM_MESSAGE_TYPE_ADMIN_REMOVED = 7;
  SYSTEM_MESSAGE_TYPE_MESSAGE_RECALLED = 8;
}
