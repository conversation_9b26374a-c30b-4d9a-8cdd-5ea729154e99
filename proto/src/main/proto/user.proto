syntax = "proto3";

package zlim.user.v1;

option java_package = "com.zlim.user.proto";
option java_outer_classname = "UserProto";
option java_multiple_files = true;
option kotlin_package = "com.zlim.user.proto";

import "common.proto";
import "google/protobuf/timestamp.proto";

// 用户服务
service UserService {
  // 获取用户信息
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  
  // 批量获取用户信息
  rpc GetUsers(GetUsersRequest) returns (GetUsersResponse);
  
  // 更新用户信息
  rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse);
  
  // 搜索用户
  rpc SearchUsers(SearchUsersRequest) returns (SearchUsersResponse);
  
  // 获取用户设置
  rpc GetUserSettings(GetUserSettingsRequest) returns (GetUserSettingsResponse);
  
  // 更新用户设置
  rpc UpdateUserSettings(UpdateUserSettingsRequest) returns (UpdateUserSettingsResponse);
  
  // 上传头像
  rpc UploadAvatar(UploadAvatarRequest) returns (UploadAvatarResponse);
  
  // 获取用户统计信息
  rpc GetUserStats(GetUserStatsRequest) returns (GetUserStatsResponse);
  
  // 删除用户账号
  rpc DeleteUser(DeleteUserRequest) returns (DeleteUserResponse);
  
  // 封禁/解封用户
  rpc BanUser(BanUserRequest) returns (BanUserResponse);
  rpc UnbanUser(UnbanUserRequest) returns (UnbanUserResponse);
}

// 获取用户信息请求
message GetUserRequest {
  oneof identifier {
    int64 user_id = 1;
    string username = 2;
    string email = 3;
    string phone = 4;
  }
  bool include_settings = 5;
  bool include_stats = 6;
}

// 获取用户信息响应
message GetUserResponse {
  zlim.common.v1.Result result = 1;
  User user = 2;
}

// 批量获取用户信息请求
message GetUsersRequest {
  repeated int64 user_ids = 1;
  bool include_settings = 2;
  bool include_stats = 3;
}

// 批量获取用户信息响应
message GetUsersResponse {
  zlim.common.v1.Result result = 1;
  repeated User users = 2;
}

// 更新用户信息请求
message UpdateUserRequest {
  int64 user_id = 1;
  optional string nickname = 2;
  optional string bio = 3;
  optional string avatar = 4;
  optional Gender gender = 5;
  optional string birthday = 6; // YYYY-MM-DD
  optional zlim.common.v1.Location location = 7;
  map<string, string> custom_fields = 8;
}

// 更新用户信息响应
message UpdateUserResponse {
  zlim.common.v1.Result result = 1;
  User user = 2;
}

// 搜索用户请求
message SearchUsersRequest {
  string query = 1;
  SearchType search_type = 2;
  zlim.common.v1.PageRequest page = 3;
  repeated UserFilter filters = 4;
}

// 搜索用户响应
message SearchUsersResponse {
  zlim.common.v1.Result result = 1;
  repeated User users = 2;
  zlim.common.v1.PageResponse page = 3;
}

// 获取用户设置请求
message GetUserSettingsRequest {
  int64 user_id = 1;
  repeated string setting_keys = 2; // 如果为空，返回所有设置
}

// 获取用户设置响应
message GetUserSettingsResponse {
  zlim.common.v1.Result result = 1;
  UserSettings settings = 2;
}

// 更新用户设置请求
message UpdateUserSettingsRequest {
  int64 user_id = 1;
  UserSettings settings = 2;
}

// 更新用户设置响应
message UpdateUserSettingsResponse {
  zlim.common.v1.Result result = 1;
  UserSettings settings = 2;
}

// 上传头像请求
message UploadAvatarRequest {
  int64 user_id = 1;
  bytes avatar_data = 2;
  string content_type = 3;
}

// 上传头像响应
message UploadAvatarResponse {
  zlim.common.v1.Result result = 1;
  string avatar_url = 2;
}

// 获取用户统计信息请求
message GetUserStatsRequest {
  int64 user_id = 1;
}

// 获取用户统计信息响应
message GetUserStatsResponse {
  zlim.common.v1.Result result = 1;
  UserStats stats = 2;
}

// 删除用户账号请求
message DeleteUserRequest {
  int64 user_id = 1;
  string reason = 2;
  bool hard_delete = 3; // true: 物理删除, false: 逻辑删除
}

// 删除用户账号响应
message DeleteUserResponse {
  zlim.common.v1.Result result = 1;
}

// 封禁用户请求
message BanUserRequest {
  int64 user_id = 1;
  string reason = 2;
  google.protobuf.Timestamp ban_until = 3; // 封禁到期时间，为空表示永久封禁
  int64 operator_id = 4;
}

// 封禁用户响应
message BanUserResponse {
  zlim.common.v1.Result result = 1;
}

// 解封用户请求
message UnbanUserRequest {
  int64 user_id = 1;
  string reason = 2;
  int64 operator_id = 3;
}

// 解封用户响应
message UnbanUserResponse {
  zlim.common.v1.Result result = 1;
}

// 用户信息
message User {
  int64 user_id = 1;
  string username = 2;
  string email = 3;
  string phone = 4;
  string nickname = 5;
  string bio = 6;
  string avatar = 7;
  Gender gender = 8;
  string birthday = 9; // YYYY-MM-DD
  zlim.common.v1.Location location = 10;
  zlim.common.v1.UserStatus status = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  google.protobuf.Timestamp last_login_at = 14;
  UserSettings settings = 15;
  UserStats stats = 16;
  map<string, string> custom_fields = 17;
  repeated string tags = 18;
}

// 性别枚举
enum Gender {
  GENDER_UNSPECIFIED = 0;
  GENDER_MALE = 1;
  GENDER_FEMALE = 2;
  GENDER_OTHER = 3;
  GENDER_PREFER_NOT_TO_SAY = 4;
}

// 搜索类型
enum SearchType {
  SEARCH_TYPE_UNSPECIFIED = 0;
  SEARCH_TYPE_USERNAME = 1;
  SEARCH_TYPE_NICKNAME = 2;
  SEARCH_TYPE_EMAIL = 3;
  SEARCH_TYPE_PHONE = 4;
  SEARCH_TYPE_ALL = 5;
}

// 用户过滤器
message UserFilter {
  FilterType type = 1;
  string value = 2;
}

// 过滤器类型
enum FilterType {
  FILTER_TYPE_UNSPECIFIED = 0;
  FILTER_TYPE_STATUS = 1;
  FILTER_TYPE_GENDER = 2;
  FILTER_TYPE_LOCATION = 3;
  FILTER_TYPE_CREATED_AFTER = 4;
  FILTER_TYPE_CREATED_BEFORE = 5;
  FILTER_TYPE_TAG = 6;
}

// 用户设置
message UserSettings {
  PrivacySettings privacy = 1;
  NotificationSettings notification = 2;
  ChatSettings chat = 3;
  string language = 4;
  string timezone = 5;
  string theme = 6;
  map<string, string> custom_settings = 7;
}

// 隐私设置
message PrivacySettings {
  bool show_phone = 1;
  bool show_email = 2;
  bool show_location = 3;
  bool allow_friend_requests = 4;
  bool allow_group_invites = 5;
  bool show_online_status = 6;
  bool show_last_seen = 7;
  FriendRequestMode friend_request_mode = 8;
}

// 好友请求模式
enum FriendRequestMode {
  FRIEND_REQUEST_MODE_UNSPECIFIED = 0;
  FRIEND_REQUEST_MODE_EVERYONE = 1;
  FRIEND_REQUEST_MODE_FRIENDS_OF_FRIENDS = 2;
  FRIEND_REQUEST_MODE_NOBODY = 3;
}

// 通知设置
message NotificationSettings {
  bool push_enabled = 1;
  bool message_notifications = 2;
  bool friend_request_notifications = 3;
  bool group_notifications = 4;
  bool system_notifications = 5;
  string quiet_hours_start = 6; // HH:MM
  string quiet_hours_end = 7;   // HH:MM
  repeated int32 quiet_days = 8; // 0=Sunday, 1=Monday, ...
}

// 聊天设置
message ChatSettings {
  bool read_receipts = 1;
  bool typing_indicators = 2;
  bool auto_download_images = 3;
  bool auto_download_videos = 4;
  bool auto_download_files = 5;
  string font_size = 6;
  bool sound_enabled = 7;
  bool vibration_enabled = 8;
}

// 用户统计信息
message UserStats {
  int64 friends_count = 1;
  int64 groups_count = 2;
  int64 messages_sent = 3;
  int64 messages_received = 4;
  int64 files_shared = 5;
  google.protobuf.Timestamp last_active = 6;
  int64 total_online_time = 7; // 总在线时间（秒）
  int64 login_count = 8;
}
