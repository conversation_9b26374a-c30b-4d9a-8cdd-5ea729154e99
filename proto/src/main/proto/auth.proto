syntax = "proto3";

package zlim.auth.v1;

option java_package = "com.zlim.auth.proto";
option java_outer_classname = "AuthProto";
option java_multiple_files = true;
option kotlin_package = "com.zlim.auth.proto";

import "common.proto";
import "google/protobuf/timestamp.proto";

// 认证服务
service AuthService {
  // 用户注册
  rpc Register(RegisterRequest) returns (RegisterResponse);
  
  // 用户登录
  rpc Login(LoginRequest) returns (LoginResponse);
  
  // 刷新Token
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
  
  // 验证Token
  rpc VerifyToken(VerifyTokenRequest) returns (VerifyTokenResponse);
  
  // 用户登出
  rpc Logout(LogoutRequest) returns (LogoutResponse);
  
  // 发送验证码
  rpc SendVerificationCode(SendVerificationCodeRequest) returns (SendVerificationCodeResponse);
  
  // 验证验证码
  rpc VerifyCode(VerifyCodeRequest) returns (VerifyCodeResponse);
  
  // 重置密码
  rpc ResetPassword(ResetPasswordRequest) returns (ResetPasswordResponse);
  
  // 修改密码
  rpc ChangePassword(ChangePasswordRequest) returns (ChangePasswordResponse);
}

// 注册请求
message RegisterRequest {
  string username = 1;
  string password = 2;
  string email = 3;
  string phone = 4;
  string verification_code = 5;
  string nickname = 6;
  zlim.common.v1.DeviceInfo device_info = 7;
  string invite_code = 8;
}

// 注册响应
message RegisterResponse {
  zlim.common.v1.Result result = 1;
  AuthToken token = 2;
  zlim.common.v1.UserInfo user_info = 3;
}

// 登录请求
message LoginRequest {
  oneof credential {
    string username = 1;
    string email = 2;
    string phone = 3;
  }
  string password = 4;
  string verification_code = 5; // 短信验证码登录
  zlim.common.v1.DeviceInfo device_info = 6;
  bool remember_me = 7;
}

// 登录响应
message LoginResponse {
  zlim.common.v1.Result result = 1;
  AuthToken token = 2;
  zlim.common.v1.UserInfo user_info = 3;
  bool is_first_login = 4;
}

// 刷新Token请求
message RefreshTokenRequest {
  string refresh_token = 1;
  zlim.common.v1.DeviceInfo device_info = 2;
}

// 刷新Token响应
message RefreshTokenResponse {
  zlim.common.v1.Result result = 1;
  AuthToken token = 2;
}

// 验证Token请求
message VerifyTokenRequest {
  string access_token = 1;
  string resource = 2; // 可选：验证特定资源权限
}

// 验证Token响应
message VerifyTokenResponse {
  zlim.common.v1.Result result = 1;
  bool valid = 2;
  int64 user_id = 3;
  repeated string permissions = 4;
  google.protobuf.Timestamp expires_at = 5;
}

// 登出请求
message LogoutRequest {
  string access_token = 1;
  string device_id = 2;
  bool logout_all_devices = 3;
}

// 登出响应
message LogoutResponse {
  zlim.common.v1.Result result = 1;
}

// 发送验证码请求
message SendVerificationCodeRequest {
  oneof target {
    string email = 1;
    string phone = 2;
  }
  VerificationCodeType type = 3;
  string captcha_token = 4; // 图形验证码
}

// 发送验证码响应
message SendVerificationCodeResponse {
  zlim.common.v1.Result result = 1;
  string code_id = 2; // 验证码ID，用于后续验证
  int32 expires_in = 3; // 过期时间（秒）
}

// 验证验证码请求
message VerifyCodeRequest {
  string code_id = 1;
  string code = 2;
  oneof target {
    string email = 3;
    string phone = 4;
  }
}

// 验证验证码响应
message VerifyCodeResponse {
  zlim.common.v1.Result result = 1;
  bool valid = 2;
  string verify_token = 3; // 验证通过后的临时token
}

// 重置密码请求
message ResetPasswordRequest {
  oneof credential {
    string email = 1;
    string phone = 2;
  }
  string verification_code = 3;
  string new_password = 4;
  string verify_token = 5;
}

// 重置密码响应
message ResetPasswordResponse {
  zlim.common.v1.Result result = 1;
}

// 修改密码请求
message ChangePasswordRequest {
  string access_token = 1;
  string old_password = 2;
  string new_password = 3;
}

// 修改密码响应
message ChangePasswordResponse {
  zlim.common.v1.Result result = 1;
}

// 认证Token
message AuthToken {
  string access_token = 1;
  string refresh_token = 2;
  string token_type = 3; // Bearer
  int64 expires_in = 4; // 过期时间（秒）
  google.protobuf.Timestamp issued_at = 5;
  repeated string scopes = 6;
}

// 验证码类型
enum VerificationCodeType {
  VERIFICATION_CODE_TYPE_UNSPECIFIED = 0;
  VERIFICATION_CODE_TYPE_REGISTER = 1;
  VERIFICATION_CODE_TYPE_LOGIN = 2;
  VERIFICATION_CODE_TYPE_RESET_PASSWORD = 3;
  VERIFICATION_CODE_TYPE_CHANGE_PHONE = 4;
  VERIFICATION_CODE_TYPE_CHANGE_EMAIL = 5;
  VERIFICATION_CODE_TYPE_DELETE_ACCOUNT = 6;
}

// JWT Claims
message JwtClaims {
  int64 user_id = 1;
  string username = 2;
  repeated string roles = 3;
  repeated string permissions = 4;
  string device_id = 5;
  zlim.common.v1.DeviceType device_type = 6;
  google.protobuf.Timestamp issued_at = 7;
  google.protobuf.Timestamp expires_at = 8;
  string issuer = 9;
  string audience = 10;
}

// 用户会话信息
message UserSession {
  string session_id = 1;
  int64 user_id = 2;
  string device_id = 3;
  zlim.common.v1.DeviceType device_type = 4;
  string ip_address = 5;
  string user_agent = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp last_active = 8;
  google.protobuf.Timestamp expires_at = 9;
  bool is_active = 10;
}
