# Gradle配置
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1g -XX:+UseG1GC
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Kotlin配置
kotlin.code.style=official
kotlin.incremental=true
kotlin.incremental.multiplatform=true
kotlin.incremental.js=true

# Spring Boot配置
spring.profiles.active=dev

# 项目信息
project.name=zlim
project.description=Instant Messaging System with Microservices Architecture
project.url=https://github.com/zlim/zlim

# 版本信息
version=1.0.0
group=com.zlim
