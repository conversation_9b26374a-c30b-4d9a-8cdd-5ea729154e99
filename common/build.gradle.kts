plugins {
    kotlin("jvm")
    kotlin("plugin.spring")
    id("org.springframework.boot")
    id("io.spring.dependency-management")
}

dependencies {
    api(project(":proto"))
    
    // Spring Boot
    api("org.springframework.boot:spring-boot-starter-web")
    api("org.springframework.boot:spring-boot-starter-data-jpa")
    api("org.springframework.boot:spring-boot-starter-data-redis")
    api("org.springframework.boot:spring-boot-starter-validation")
    api("org.springframework.boot:spring-boot-starter-security")
    
    // MyBatis Plus
    api("com.baomidou:mybatis-plus-boot-starter:${property("mybatisPlusVersion")}")
    api("com.baomidou:mybatis-plus-generator:${property("mybatisPlusVersion")}")
    
    // 数据库驱动
    api("org.postgresql:postgresql")
    api("com.zaxxer:HikariCP")
    
    // Redis
    api("org.redisson:redisson-spring-boot-starter:${property("redissonVersion")}")
    
    // RocketMQ
    api("org.apache.rocketmq:rocketmq-spring-boot-starter:${property("rocketmqVersion")}")
    
    // gRPC
    api("io.grpc:grpc-netty-shaded")
    api("io.grpc:grpc-protobuf")
    api("io.grpc:grpc-stub")
    api("io.grpc:grpc-kotlin-stub")
    
    // Netty
    api("io.netty:netty-all:${property("nettyVersion")}")
    
    // JWT
    api("io.jsonwebtoken:jjwt-api:${property("jjwtVersion")}")
    api("io.jsonwebtoken:jjwt-impl:${property("jjwtVersion")}")
    api("io.jsonwebtoken:jjwt-jackson:${property("jjwtVersion")}")
    
    // MinIO
    api("io.minio:minio:${property("minioVersion")}")
    
    // 工具类
    api("org.apache.commons:commons-lang3")
    api("org.apache.commons:commons-pool2")
    api("commons-codec:commons-codec")
    api("com.google.guava:guava:33.2.1-jre")
    
    // 文档
    api("com.github.xiaoymin:knife4j-openapi3-jakarta-spring-boot-starter:${property("knife4jVersion")}")
    
    // 序列化
    api("com.fasterxml.jackson.module:jackson-module-kotlin")
    api("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    
    // 测试
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.security:spring-security-test")
}
