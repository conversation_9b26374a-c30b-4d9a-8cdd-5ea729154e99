# 通用消息 (默认英文)
common.success=Success
common.failed=Failed
common.created=Created successfully
common.updated=Updated successfully
common.deleted=Deleted successfully
common.not_found=Resource not found
common.unauthorized=Unauthorized access
common.forbidden=Access forbidden
common.invalid_parameter=Invalid parameter
common.internal_error=Internal server error
common.service_unavailable=Service unavailable
common.rate_limited=Rate limit exceeded
common.conflict=Resource conflict

# 验证消息
validation.required=This field is required
validation.invalid=Invalid value
validation.too_long=Value is too long
validation.too_short=Value is too short
validation.invalid_format=Invalid format
validation.invalid_email=Invalid email format
validation.invalid_phone=Invalid phone number format
validation.password_too_weak=Password is too weak
validation.passwords_not_match=Passwords do not match

# 认证相关
auth.login_success=Login successful
auth.login_failed=Login failed
auth.logout_success=Logout successful
auth.register_success=Registration successful
auth.register_failed=Registration failed
auth.token_expired=Token has expired
auth.invalid_token=Invalid token
auth.invalid_credentials=Invalid username or password
auth.account_locked=Account is locked
auth.account_disabled=Account is disabled
auth.verification_code_sent=Verification code sent
auth.verification_code_invalid=Invalid verification code
auth.verification_code_expired=Verification code has expired
auth.password_reset_success=Password reset successful
auth.password_changed=Password changed successfully

# 用户相关
user.created=User created successfully
user.updated=User profile updated successfully
user.deleted=User account deleted
user.not_found=User not found
user.already_exists=User already exists
user.profile_incomplete=User profile is incomplete
user.avatar_updated=Avatar updated successfully
user.settings_updated=Settings updated successfully

# 消息相关
message.sent=Message sent successfully
message.received=Message received
message.read=Message marked as read
message.deleted=Message deleted
message.recalled=Message recalled
message.not_found=Message not found
message.too_long=Message is too long (max {0} characters)
message.send_failed=Failed to send message
message.invalid_type=Invalid message type
message.conversation_not_found=Conversation not found

# 社交相关
social.friend_request_sent=Friend request sent
social.friend_request_accepted=Friend request accepted
social.friend_request_rejected=Friend request rejected
social.friend_added=Friend added successfully
social.friend_removed=Friend removed
social.friend_not_found=Friend not found
social.friend_already_exists=Already friends
social.user_blocked=User blocked
social.user_unblocked=User unblocked
social.group_created=Group created successfully
social.group_updated=Group updated successfully
social.group_deleted=Group deleted
social.group_joined=Joined group successfully
social.group_left=Left group successfully
social.group_not_found=Group not found
social.group_full=Group is full (max {0} members)
social.not_group_member=You are not a member of this group
social.insufficient_permission=Insufficient permission
social.channel_subscribed=Subscribed to channel
social.channel_unsubscribed=Unsubscribed from channel

# 媒体相关
media.file_uploaded=File uploaded successfully
media.file_deleted=File deleted successfully
media.file_not_found=File not found
media.file_too_large=File is too large (max {0} MB)
media.invalid_file_type=Invalid file type (allowed: {0})
media.upload_failed=File upload failed
media.processing=File is being processed
media.thumbnail_generated=Thumbnail generated successfully
media.conversion_started=Media conversion started

# 推送相关
push.sent=Push notification sent
push.failed=Push notification failed
push.token_registered=Push token registered
push.token_unregistered=Push token unregistered
push.settings_updated=Push settings updated

# 管理相关
admin.user_banned=User banned successfully
admin.user_unbanned=User unbanned successfully
admin.operation_completed=Operation completed successfully
admin.permission_required=Admin permission required
admin.audit_logged=Action logged for audit

# 通知相关
notification.sent=Notification sent
notification.read=Notification marked as read
notification.deleted=Notification deleted
notification.settings_updated=Notification settings updated

# 系统消息
system.maintenance=System is under maintenance
system.feature_disabled=This feature is currently disabled
system.update_available=System update available
system.welcome=Welcome to ZLIM!

# 错误消息
error.1000=Success
error.1001=Invalid parameter: {0}
error.1002=Authentication required
error.1003=Access denied
error.1004=Resource not found: {0}
error.1005=Resource conflict: {0}
error.1006=Internal server error
error.1007=Service temporarily unavailable
error.1008=Too many requests, please try again later

error.2001=Invalid or malformed token
error.2002=Token has expired, please login again
error.2003=Invalid username or password
error.2004=Account is locked due to multiple failed attempts
error.2005=Account is disabled, please contact support
error.2006=Invalid verification code
error.2007=Verification code has expired
error.2008=Too many login attempts, please try again later
error.2009=Password does not meet security requirements
error.2010=Captcha verification required
error.2011=Invalid captcha code

error.3001=User not found
error.3002=Username already exists
error.3003=Invalid username format
error.3004=Invalid password format
error.3005=Invalid email format
error.3006=Invalid phone number format
error.3007=Email address already registered
error.3008=Phone number already registered
error.3009=User account is banned
error.3010=User account has been deleted

error.4001=Message not found
error.4002=Message exceeds maximum length
error.4003=Unsupported message type
error.4004=Failed to send message
error.4005=Message already marked as read
error.4006=Failed to recall message
error.4007=Message recall time limit exceeded
error.4008=Conversation not found
error.4009=Conversation is archived

error.5001=Friend not found
error.5002=Already friends with this user
error.5003=Friend request not found
error.5004=Friend request already sent
error.5005=Friend request has expired
error.5006=Cannot add yourself as friend
error.5007=User has blocked you
error.5008=User already blocked
error.5009=User is not blocked
error.5010=Group not found
error.5011=Group has reached maximum capacity
error.5012=You are not a member of this group
error.5013=Already a member of this group
error.5014=Insufficient permission for this action

error.6001=File size exceeds limit
error.6002=File type not supported
error.6003=File upload failed
error.6004=File not found
error.6005=File is corrupted
error.6006=Failed to generate thumbnail
error.6007=Media processing failed
error.6008=Storage quota exceeded

error.7001=Invalid push token
error.7002=Failed to send push notification
error.7003=Push notification quota exceeded
error.7004=Device not registered for push notifications
error.7005=Push notifications are disabled

error.8001=Administrator permission required
error.8002=Operation not allowed
error.8003=Failed to log audit trail
error.8004=System is under maintenance
error.8005=Feature is currently disabled

error.9001=Notification not found
error.9002=Failed to send notification
error.9003=Notification template not found
error.9004=Invalid notification template
error.9005=Notification quota exceeded
