package com.zlim.common.config

import com.zlim.common.enums.ErrorCode
import com.zlim.common.exception.*
import com.zlim.common.response.ApiResponse
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.validation.BindException
import org.springframework.validation.FieldError
import org.springframework.web.HttpMediaTypeNotSupportedException
import org.springframework.web.HttpRequestMethodNotSupportedException
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.MissingServletRequestParameterException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException
import org.springframework.web.multipart.MaxUploadSizeExceededException
import org.springframework.web.servlet.NoHandlerFoundException
import javax.servlet.http.HttpServletRequest
import javax.validation.ConstraintViolationException

/**
 * 全局异常处理器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestControllerAdvice
class GlobalExceptionHandler {
    
    private val logger = LoggerFactory.getLogger(GlobalExceptionHandler::class.java)
    
    /**
     * 业务异常处理
     */
    @ExceptionHandler(BusinessException::class)
    fun handleBusinessException(
        ex: BusinessException,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Business exception: {}", ex.message, ex)
        
        val response = if (ex.details != null) {
            ApiResponse.error<Nothing>(ex.errorCode, ex.errorMessage, ex.details)
        } else {
            ApiResponse.error<Nothing>(ex.errorCode, ex.errorMessage)
        }
        
        return ResponseEntity.status(ex.errorCode.httpStatus).body(response)
    }
    
    /**
     * 参数验证异常处理
     */
    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleValidationException(
        ex: MethodArgumentNotValidException,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Validation exception: {}", ex.message)
        
        val errors = mutableMapOf<String, Any>()
        ex.bindingResult.allErrors.forEach { error ->
            when (error) {
                is FieldError -> {
                    errors[error.field] = error.defaultMessage ?: "Invalid value"
                }
                else -> {
                    errors[error.objectName] = error.defaultMessage ?: "Invalid value"
                }
            }
        }
        
        val response = ApiResponse.validationError<Nothing>(errors)
        return ResponseEntity.badRequest().body(response)
    }
    
    /**
     * 绑定异常处理
     */
    @ExceptionHandler(BindException::class)
    fun handleBindException(
        ex: BindException,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Bind exception: {}", ex.message)
        
        val errors = mutableMapOf<String, Any>()
        ex.bindingResult.allErrors.forEach { error ->
            when (error) {
                is FieldError -> {
                    errors[error.field] = error.defaultMessage ?: "Invalid value"
                }
                else -> {
                    errors[error.objectName] = error.defaultMessage ?: "Invalid value"
                }
            }
        }
        
        val response = ApiResponse.validationError<Nothing>(errors)
        return ResponseEntity.badRequest().body(response)
    }
    
    /**
     * 约束违反异常处理
     */
    @ExceptionHandler(ConstraintViolationException::class)
    fun handleConstraintViolationException(
        ex: ConstraintViolationException,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Constraint violation exception: {}", ex.message)
        
        val errors = mutableMapOf<String, Any>()
        ex.constraintViolations.forEach { violation ->
            val propertyPath = violation.propertyPath.toString()
            val fieldName = if (propertyPath.contains('.')) {
                propertyPath.substringAfterLast('.')
            } else {
                propertyPath
            }
            errors[fieldName] = violation.message
        }
        
        val response = ApiResponse.validationError<Nothing>(errors)
        return ResponseEntity.badRequest().body(response)
    }
    
    /**
     * 缺少请求参数异常处理
     */
    @ExceptionHandler(MissingServletRequestParameterException::class)
    fun handleMissingParameterException(
        ex: MissingServletRequestParameterException,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Missing parameter exception: {}", ex.message)
        
        val details = mapOf(
            "parameter" to ex.parameterName,
            "type" to ex.parameterType
        )
        
        val response = ApiResponse.error<Nothing>(
            ErrorCode.INVALID_PARAMETER,
            "Missing required parameter: ${ex.parameterName}",
            details
        )
        return ResponseEntity.badRequest().body(response)
    }
    
    /**
     * 参数类型不匹配异常处理
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException::class)
    fun handleTypeMismatchException(
        ex: MethodArgumentTypeMismatchException,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Type mismatch exception: {}", ex.message)
        
        val details = mapOf(
            "parameter" to (ex.name ?: "unknown"),
            "value" to (ex.value?.toString() ?: "null"),
            "requiredType" to (ex.requiredType?.simpleName ?: "unknown")
        )
        
        val response = ApiResponse.error<Nothing>(
            ErrorCode.INVALID_PARAMETER,
            "Invalid parameter type for: ${ex.name}",
            details
        )
        return ResponseEntity.badRequest().body(response)
    }
    
    /**
     * HTTP消息不可读异常处理
     */
    @ExceptionHandler(HttpMessageNotReadableException::class)
    fun handleMessageNotReadableException(
        ex: HttpMessageNotReadableException,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Message not readable exception: {}", ex.message)
        
        val response = ApiResponse.error<Nothing>(
            ErrorCode.INVALID_PARAMETER,
            "Invalid request body format"
        )
        return ResponseEntity.badRequest().body(response)
    }
    
    /**
     * 不支持的媒体类型异常处理
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException::class)
    fun handleMediaTypeNotSupportedException(
        ex: HttpMediaTypeNotSupportedException,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Media type not supported exception: {}", ex.message)
        
        val details = mapOf(
            "contentType" to (ex.contentType?.toString() ?: "unknown"),
            "supportedTypes" to ex.supportedMediaTypes.map { it.toString() }
        )
        
        val response = ApiResponse.error<Nothing>(
            ErrorCode.INVALID_PARAMETER,
            "Unsupported media type: ${ex.contentType}",
            details
        )
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body(response)
    }
    
    /**
     * 不支持的请求方法异常处理
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException::class)
    fun handleMethodNotSupportedException(
        ex: HttpRequestMethodNotSupportedException,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Method not supported exception: {}", ex.message)
        
        val details = mapOf(
            "method" to ex.method,
            "supportedMethods" to (ex.supportedMethods?.toList() ?: emptyList<String>())
        )
        
        val response = ApiResponse.error<Nothing>(
            ErrorCode.INVALID_PARAMETER,
            "Unsupported request method: ${ex.method}",
            details
        )
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(response)
    }
    
    /**
     * 404异常处理
     */
    @ExceptionHandler(NoHandlerFoundException::class)
    fun handleNoHandlerFoundException(
        ex: NoHandlerFoundException,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("No handler found exception: {}", ex.message)
        
        val details = mapOf(
            "method" to ex.httpMethod,
            "url" to ex.requestURL
        )
        
        val response = ApiResponse.error<Nothing>(
            ErrorCode.NOT_FOUND,
            "Endpoint not found: ${ex.httpMethod} ${ex.requestURL}",
            details
        )
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response)
    }
    
    /**
     * 文件上传大小超限异常处理
     */
    @ExceptionHandler(MaxUploadSizeExceededException::class)
    fun handleMaxUploadSizeExceededException(
        ex: MaxUploadSizeExceededException,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Max upload size exceeded exception: {}", ex.message)
        
        val details = mapOf(
            "maxUploadSize" to ex.maxUploadSize
        )
        
        val response = ApiResponse.error<Nothing>(
            ErrorCode.FILE_TOO_LARGE,
            "File size exceeds maximum allowed size",
            details
        )
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(response)
    }
    
    /**
     * 通用异常处理
     */
    @ExceptionHandler(Exception::class)
    fun handleGenericException(
        ex: Exception,
        request: HttpServletRequest
    ): ResponseEntity<ApiResponse<Nothing>> {
        logger.error("Unexpected exception: {}", ex.message, ex)
        
        val response = ApiResponse.internalError<Nothing>()
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response)
    }
}
