package com.zlim.common.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.zlim.common.utils.JsonUtils
import org.springframework.context.MessageSource
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.context.support.ReloadableResourceBundleMessageSource
import org.springframework.data.redis.connection.RedisConnectionFactory
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer
import org.springframework.data.redis.serializer.StringRedisSerializer
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.web.servlet.LocaleResolver
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver
import java.nio.charset.StandardCharsets
import java.util.*
import java.util.concurrent.Executor

/**
 * 公共配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableAsync
@EnableScheduling
class CommonConfiguration {
    
    /**
     * 国际化消息源配置
     */
    @Bean
    @Primary
    fun messageSource(): MessageSource {
        val messageSource = ReloadableResourceBundleMessageSource()
        messageSource.setBasename("classpath:messages")
        messageSource.setDefaultEncoding(StandardCharsets.UTF_8.name())
        messageSource.setDefaultLocale(Locale.SIMPLIFIED_CHINESE)
        messageSource.setCacheSeconds(3600) // 缓存1小时
        messageSource.setFallbackToSystemLocale(false)
        return messageSource
    }
    
    /**
     * 语言解析器配置
     */
    @Bean
    fun localeResolver(): LocaleResolver {
        val resolver = AcceptHeaderLocaleResolver()
        resolver.defaultLocale = Locale.SIMPLIFIED_CHINESE
        resolver.supportedLocales = listOf(
            Locale.SIMPLIFIED_CHINESE,
            Locale.US,
            Locale.ENGLISH
        )
        return resolver
    }
    
    /**
     * Jackson ObjectMapper配置
     */
    @Bean
    @Primary
    fun objectMapper(): ObjectMapper {
        return JsonUtils.objectMapper
    }
    
    /**
     * Redis模板配置
     */
    @Bean
    fun redisTemplate(connectionFactory: RedisConnectionFactory): RedisTemplate<String, Any> {
        val template = RedisTemplate<String, Any>()
        template.connectionFactory = connectionFactory
        
        // 使用String序列化器作为key序列化器
        val stringSerializer = StringRedisSerializer()
        template.keySerializer = stringSerializer
        template.hashKeySerializer = stringSerializer
        
        // 使用Jackson序列化器作为value序列化器
        val jsonSerializer = GenericJackson2JsonRedisSerializer(JsonUtils.objectMapper)
        template.valueSerializer = jsonSerializer
        template.hashValueSerializer = jsonSerializer
        
        template.afterPropertiesSet()
        return template
    }
    
    /**
     * 异步任务执行器配置
     */
    @Bean("taskExecutor")
    fun taskExecutor(): Executor {
        val executor = ThreadPoolTaskExecutor()
        executor.corePoolSize = 10
        executor.maxPoolSize = 50
        executor.queueCapacity = 200
        executor.keepAliveSeconds = 60
        executor.setThreadNamePrefix("zlim-async-")
        executor.setRejectedExecutionHandler { runnable, _ ->
            // 当线程池满时，在调用线程中执行
            runnable.run()
        }
        executor.initialize()
        return executor
    }
    
    /**
     * 消息处理异步执行器
     */
    @Bean("messageExecutor")
    fun messageExecutor(): Executor {
        val executor = ThreadPoolTaskExecutor()
        executor.corePoolSize = 20
        executor.maxPoolSize = 100
        executor.queueCapacity = 500
        executor.keepAliveSeconds = 60
        executor.setThreadNamePrefix("zlim-message-")
        executor.setRejectedExecutionHandler { runnable, _ ->
            // 当线程池满时，在调用线程中执行
            runnable.run()
        }
        executor.initialize()
        return executor
    }
    
    /**
     * 推送任务执行器
     */
    @Bean("pushExecutor")
    fun pushExecutor(): Executor {
        val executor = ThreadPoolTaskExecutor()
        executor.corePoolSize = 5
        executor.maxPoolSize = 20
        executor.queueCapacity = 100
        executor.keepAliveSeconds = 60
        executor.setThreadNamePrefix("zlim-push-")
        executor.setRejectedExecutionHandler { runnable, _ ->
            // 当线程池满时，在调用线程中执行
            runnable.run()
        }
        executor.initialize()
        return executor
    }
    
    /**
     * 媒体处理执行器
     */
    @Bean("mediaExecutor")
    fun mediaExecutor(): Executor {
        val executor = ThreadPoolTaskExecutor()
        executor.corePoolSize = 3
        executor.maxPoolSize = 10
        executor.queueCapacity = 50
        executor.keepAliveSeconds = 300
        executor.setThreadNamePrefix("zlim-media-")
        executor.setRejectedExecutionHandler { runnable, _ ->
            // 当线程池满时，在调用线程中执行
            runnable.run()
        }
        executor.initialize()
        return executor
    }
}
