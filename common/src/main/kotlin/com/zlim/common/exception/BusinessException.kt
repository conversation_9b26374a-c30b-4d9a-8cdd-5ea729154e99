package com.zlim.common.exception

import com.zlim.common.enums.ErrorCode

/**
 * 业务异常基类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
open class BusinessException : RuntimeException {
    
    val errorCode: ErrorCode
    val errorMessage: String
    val details: Map<String, Any>?
    
    constructor(errorCode: ErrorCode) : super(errorCode.message) {
        this.errorCode = errorCode
        this.errorMessage = errorCode.message
        this.details = null
    }
    
    constructor(errorCode: ErrorCode, message: String) : super(message) {
        this.errorCode = errorCode
        this.errorMessage = message
        this.details = null
    }
    
    constructor(errorCode: ErrorCode, message: String, cause: Throwable) : super(message, cause) {
        this.errorCode = errorCode
        this.errorMessage = message
        this.details = null
    }
    
    constructor(errorCode: ErrorCode, message: String, details: Map<String, Any>) : super(message) {
        this.errorCode = errorCode
        this.errorMessage = message
        this.details = details
    }
    
    constructor(errorCode: ErrorCode, message: String, details: Map<String, Any>, cause: Throwable) : super(message, cause) {
        this.errorCode = errorCode
        this.errorMessage = message
        this.details = details
    }
    
    override fun toString(): String {
        return "BusinessException(errorCode=$errorCode, errorMessage='$errorMessage', details=$details)"
    }
}

/**
 * 参数验证异常
 */
class ValidationException : BusinessException {
    constructor(message: String) : super(ErrorCode.INVALID_PARAMETER, message)
    constructor(message: String, details: Map<String, Any>) : super(ErrorCode.INVALID_PARAMETER, message, details)
}

/**
 * 认证异常
 */
class AuthenticationException : BusinessException {
    constructor(message: String) : super(ErrorCode.UNAUTHORIZED, message)
    constructor(errorCode: ErrorCode, message: String) : super(errorCode, message)
}

/**
 * 授权异常
 */
class AuthorizationException : BusinessException {
    constructor(message: String) : super(ErrorCode.FORBIDDEN, message)
    constructor(errorCode: ErrorCode, message: String) : super(errorCode, message)
}

/**
 * 资源未找到异常
 */
class ResourceNotFoundException : BusinessException {
    constructor(message: String) : super(ErrorCode.NOT_FOUND, message)
    constructor(resourceType: String, resourceId: String) : super(
        ErrorCode.NOT_FOUND, 
        "$resourceType not found: $resourceId"
    )
}

/**
 * 资源冲突异常
 */
class ResourceConflictException : BusinessException {
    constructor(message: String) : super(ErrorCode.CONFLICT, message)
    constructor(resourceType: String, resourceId: String) : super(
        ErrorCode.CONFLICT, 
        "$resourceType already exists: $resourceId"
    )
}

/**
 * 服务不可用异常
 */
class ServiceUnavailableException : BusinessException {
    constructor(message: String) : super(ErrorCode.SERVICE_UNAVAILABLE, message)
    constructor(serviceName: String) : super(
        ErrorCode.SERVICE_UNAVAILABLE, 
        "Service unavailable: $serviceName"
    )
}

/**
 * 限流异常
 */
class RateLimitException : BusinessException {
    constructor(message: String) : super(ErrorCode.RATE_LIMITED, message)
    constructor() : super(ErrorCode.RATE_LIMITED, "Rate limit exceeded")
}

/**
 * 用户相关异常
 */
class UserException : BusinessException {
    constructor(errorCode: ErrorCode, message: String) : super(errorCode, message)
    
    companion object {
        fun userNotFound(userId: Long) = UserException(ErrorCode.USER_NOT_FOUND, "User not found: $userId")
        fun userAlreadyExists(username: String) = UserException(ErrorCode.USER_ALREADY_EXISTS, "User already exists: $username")
        fun invalidCredentials() = UserException(ErrorCode.INVALID_CREDENTIALS, "Invalid username or password")
        fun accountLocked() = UserException(ErrorCode.ACCOUNT_LOCKED, "Account is locked")
        fun accountDisabled() = UserException(ErrorCode.ACCOUNT_DISABLED, "Account is disabled")
    }
}

/**
 * 消息相关异常
 */
class MessageException : BusinessException {
    constructor(errorCode: ErrorCode, message: String) : super(errorCode, message)
    
    companion object {
        fun messageNotFound(messageId: String) = MessageException(ErrorCode.MESSAGE_NOT_FOUND, "Message not found: $messageId")
        fun messageTooLong(maxLength: Int) = MessageException(ErrorCode.MESSAGE_TOO_LONG, "Message too long, max length: $maxLength")
        fun invalidMessageType(type: String) = MessageException(ErrorCode.INVALID_MESSAGE_TYPE, "Invalid message type: $type")
        fun messageSendFailed(reason: String) = MessageException(ErrorCode.MESSAGE_SEND_FAILED, "Message send failed: $reason")
    }
}

/**
 * 社交相关异常
 */
class SocialException : BusinessException {
    constructor(errorCode: ErrorCode, message: String) : super(errorCode, message)
    
    companion object {
        fun friendNotFound(friendId: Long) = SocialException(ErrorCode.FRIEND_NOT_FOUND, "Friend not found: $friendId")
        fun friendAlreadyExists(friendId: Long) = SocialException(ErrorCode.FRIEND_ALREADY_EXISTS, "Friend already exists: $friendId")
        fun groupNotFound(groupId: Long) = SocialException(ErrorCode.GROUP_NOT_FOUND, "Group not found: $groupId")
        fun groupFull(maxMembers: Int) = SocialException(ErrorCode.GROUP_FULL, "Group is full, max members: $maxMembers")
        fun notGroupMember(groupId: Long) = SocialException(ErrorCode.NOT_GROUP_MEMBER, "Not a member of group: $groupId")
        fun insufficientPermission(action: String) = SocialException(ErrorCode.INSUFFICIENT_PERMISSION, "Insufficient permission for action: $action")
    }
}

/**
 * 媒体相关异常
 */
class MediaException : BusinessException {
    constructor(errorCode: ErrorCode, message: String) : super(errorCode, message)
    
    companion object {
        fun fileTooLarge(maxSize: Long) = MediaException(ErrorCode.FILE_TOO_LARGE, "File too large, max size: $maxSize bytes")
        fun invalidFileType(allowedTypes: List<String>) = MediaException(ErrorCode.INVALID_FILE_TYPE, "Invalid file type, allowed: ${allowedTypes.joinToString()}")
        fun uploadFailed(reason: String) = MediaException(ErrorCode.UPLOAD_FAILED, "Upload failed: $reason")
        fun fileNotFound(fileId: String) = MediaException(ErrorCode.FILE_NOT_FOUND, "File not found: $fileId")
    }
}
