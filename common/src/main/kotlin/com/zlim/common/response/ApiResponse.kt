package com.zlim.common.response

import com.fasterxml.jackson.annotation.JsonInclude
import com.zlim.common.enums.ErrorCode
import java.time.Instant

/**
 * 统一API响应格式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class ApiResponse<T>(
    val success: Boolean,
    val errorCode: String? = null,
    val message: String? = null,
    val data: T? = null,
    val details: Map<String, Any>? = null,
    val timestamp: Long = Instant.now().epochSecond
) {
    
    companion object {
        /**
         * 成功响应
         */
        fun <T> success(data: T? = null): ApiResponse<T> {
            return ApiResponse(
                success = true,
                errorCode = ErrorCode.SUCCESS.code,
                message = "Success",
                data = data
            )
        }
        
        /**
         * 成功响应（带消息）
         */
        fun <T> success(data: T?, message: String): ApiResponse<T> {
            return ApiResponse(
                success = true,
                errorCode = ErrorCode.SUCCESS.code,
                message = message,
                data = data
            )
        }
        
        /**
         * 失败响应
         */
        fun <T> error(errorCode: ErrorCode): ApiResponse<T> {
            return ApiResponse(
                success = false,
                errorCode = errorCode.code,
                message = errorCode.message
            )
        }
        
        /**
         * 失败响应（自定义消息）
         */
        fun <T> error(errorCode: ErrorCode, message: String): ApiResponse<T> {
            return ApiResponse(
                success = false,
                errorCode = errorCode.code,
                message = message
            )
        }
        
        /**
         * 失败响应（带详情）
         */
        fun <T> error(errorCode: ErrorCode, message: String, details: Map<String, Any>): ApiResponse<T> {
            return ApiResponse(
                success = false,
                errorCode = errorCode.code,
                message = message,
                details = details
            )
        }
        
        /**
         * 参数验证失败响应
         */
        fun <T> validationError(details: Map<String, Any>): ApiResponse<T> {
            return ApiResponse(
                success = false,
                errorCode = ErrorCode.INVALID_PARAMETER.code,
                message = "Validation failed",
                details = details
            )
        }
        
        /**
         * 未授权响应
         */
        fun <T> unauthorized(): ApiResponse<T> {
            return error(ErrorCode.UNAUTHORIZED)
        }
        
        /**
         * 禁止访问响应
         */
        fun <T> forbidden(): ApiResponse<T> {
            return error(ErrorCode.FORBIDDEN)
        }
        
        /**
         * 资源未找到响应
         */
        fun <T> notFound(): ApiResponse<T> {
            return error(ErrorCode.NOT_FOUND)
        }
        
        /**
         * 资源冲突响应
         */
        fun <T> conflict(): ApiResponse<T> {
            return error(ErrorCode.CONFLICT)
        }
        
        /**
         * 服务器内部错误响应
         */
        fun <T> internalError(): ApiResponse<T> {
            return error(ErrorCode.INTERNAL_ERROR)
        }
        
        /**
         * 服务不可用响应
         */
        fun <T> serviceUnavailable(): ApiResponse<T> {
            return error(ErrorCode.SERVICE_UNAVAILABLE)
        }
        
        /**
         * 限流响应
         */
        fun <T> rateLimited(): ApiResponse<T> {
            return error(ErrorCode.RATE_LIMITED)
        }
    }
}

/**
 * 分页响应
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class PageResponse<T>(
    val content: List<T>,
    val page: Int,
    val size: Int,
    val total: Long,
    val totalPages: Int,
    val hasNext: Boolean,
    val hasPrevious: Boolean,
    val first: Boolean,
    val last: Boolean
) {
    companion object {
        fun <T> of(
            content: List<T>,
            page: Int,
            size: Int,
            total: Long
        ): PageResponse<T> {
            val totalPages = if (size == 0) 1 else ((total + size - 1) / size).toInt()
            return PageResponse(
                content = content,
                page = page,
                size = size,
                total = total,
                totalPages = totalPages,
                hasNext = page < totalPages - 1,
                hasPrevious = page > 0,
                first = page == 0,
                last = page == totalPages - 1
            )
        }
        
        fun <T> empty(page: Int = 0, size: Int = 10): PageResponse<T> {
            return of(emptyList(), page, size, 0)
        }
    }
}

/**
 * 列表响应
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class ListResponse<T>(
    val items: List<T>,
    val total: Long? = null,
    val hasMore: Boolean? = null,
    val nextCursor: String? = null
) {
    companion object {
        fun <T> of(items: List<T>): ListResponse<T> {
            return ListResponse(items = items, total = items.size.toLong())
        }
        
        fun <T> of(items: List<T>, total: Long): ListResponse<T> {
            return ListResponse(items = items, total = total)
        }
        
        fun <T> of(items: List<T>, hasMore: Boolean, nextCursor: String? = null): ListResponse<T> {
            return ListResponse(items = items, hasMore = hasMore, nextCursor = nextCursor)
        }
        
        fun <T> empty(): ListResponse<T> {
            return ListResponse(items = emptyList(), total = 0)
        }
    }
}

/**
 * 批量操作响应
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class BatchResponse<T>(
    val results: List<BatchResult<T>>,
    val successCount: Int,
    val failureCount: Int,
    val totalCount: Int
) {
    companion object {
        fun <T> of(results: List<BatchResult<T>>): BatchResponse<T> {
            val successCount = results.count { it.success }
            val failureCount = results.count { !it.success }
            return BatchResponse(
                results = results,
                successCount = successCount,
                failureCount = failureCount,
                totalCount = results.size
            )
        }
    }
}

/**
 * 批量操作单个结果
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class BatchResult<T>(
    val success: Boolean,
    val data: T? = null,
    val errorCode: String? = null,
    val errorMessage: String? = null,
    val index: Int? = null
) {
    companion object {
        fun <T> success(data: T, index: Int? = null): BatchResult<T> {
            return BatchResult(success = true, data = data, index = index)
        }
        
        fun <T> error(errorCode: ErrorCode, errorMessage: String, index: Int? = null): BatchResult<T> {
            return BatchResult(
                success = false,
                errorCode = errorCode.code,
                errorMessage = errorMessage,
                index = index
            )
        }
    }
}
