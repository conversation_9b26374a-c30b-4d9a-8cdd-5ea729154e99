package com.zlim.common.i18n

import org.springframework.context.MessageSource
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.stereotype.Service
import java.util.*

/**
 * 国际化消息服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
class MessageService(
    private val messageSource: MessageSource
) {
    
    /**
     * 获取国际化消息
     */
    fun getMessage(key: String, vararg args: Any?): String {
        return getMessage(key, LocaleContextHolder.getLocale(), *args)
    }
    
    /**
     * 获取指定语言的国际化消息
     */
    fun getMessage(key: String, locale: Locale, vararg args: Any?): String {
        return try {
            messageSource.getMessage(key, args, locale)
        } catch (e: Exception) {
            // 如果找不到消息，返回key本身
            key
        }
    }
    
    /**
     * 获取国际化消息，如果找不到则返回默认消息
     */
    fun getMessage(key: String, defaultMessage: String, vararg args: Any?): String {
        return getMessage(key, defaultMessage, LocaleContextHolder.getLocale(), *args)
    }
    
    /**
     * 获取指定语言的国际化消息，如果找不到则返回默认消息
     */
    fun getMessage(key: String, defaultMessage: String, locale: Locale, vararg args: Any?): String {
        return try {
            messageSource.getMessage(key, args, defaultMessage, locale)
        } catch (e: Exception) {
            defaultMessage
        }
    }
    
    /**
     * 检查消息key是否存在
     */
    fun hasMessage(key: String): Boolean {
        return hasMessage(key, LocaleContextHolder.getLocale())
    }
    
    /**
     * 检查指定语言的消息key是否存在
     */
    fun hasMessage(key: String, locale: Locale): Boolean {
        return try {
            messageSource.getMessage(key, null, locale)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取错误码对应的国际化消息
     */
    fun getErrorMessage(errorCode: String, vararg args: Any?): String {
        val key = "error.$errorCode"
        return getMessage(key, "Unknown error: $errorCode", *args)
    }
    
    /**
     * 获取验证错误消息
     */
    fun getValidationMessage(field: String, constraint: String, vararg args: Any?): String {
        val key = "validation.$field.$constraint"
        val fallbackKey = "validation.$constraint"
        
        return if (hasMessage(key)) {
            getMessage(key, *args)
        } else {
            getMessage(fallbackKey, "Validation failed for $field", *args)
        }
    }
    
    /**
     * 获取业务消息
     */
    fun getBusinessMessage(module: String, action: String, vararg args: Any?): String {
        val key = "business.$module.$action"
        return getMessage(key, "Business operation: $module.$action", *args)
    }
    
    /**
     * 获取系统消息
     */
    fun getSystemMessage(type: String, vararg args: Any?): String {
        val key = "system.$type"
        return getMessage(key, "System message: $type", *args)
    }
    
    /**
     * 获取通知消息
     */
    fun getNotificationMessage(type: String, vararg args: Any?): String {
        val key = "notification.$type"
        return getMessage(key, "Notification: $type", *args)
    }
    
    /**
     * 批量获取消息
     */
    fun getMessages(keys: List<String>): Map<String, String> {
        val locale = LocaleContextHolder.getLocale()
        return keys.associateWith { key ->
            getMessage(key, locale)
        }
    }
    
    /**
     * 批量获取消息（指定语言）
     */
    fun getMessages(keys: List<String>, locale: Locale): Map<String, String> {
        return keys.associateWith { key ->
            getMessage(key, locale)
        }
    }
    
    companion object {
        // 常用的消息key常量
        const val SUCCESS = "common.success"
        const val FAILED = "common.failed"
        const val CREATED = "common.created"
        const val UPDATED = "common.updated"
        const val DELETED = "common.deleted"
        const val NOT_FOUND = "common.not_found"
        const val UNAUTHORIZED = "common.unauthorized"
        const val FORBIDDEN = "common.forbidden"
        const val INVALID_PARAMETER = "common.invalid_parameter"
        const val INTERNAL_ERROR = "common.internal_error"
        
        // 用户相关消息
        const val USER_CREATED = "user.created"
        const val USER_UPDATED = "user.updated"
        const val USER_DELETED = "user.deleted"
        const val USER_NOT_FOUND = "user.not_found"
        const val USER_ALREADY_EXISTS = "user.already_exists"
        const val INVALID_CREDENTIALS = "user.invalid_credentials"
        const val ACCOUNT_LOCKED = "user.account_locked"
        const val ACCOUNT_DISABLED = "user.account_disabled"
        
        // 消息相关
        const val MESSAGE_SENT = "message.sent"
        const val MESSAGE_RECEIVED = "message.received"
        const val MESSAGE_READ = "message.read"
        const val MESSAGE_DELETED = "message.deleted"
        const val MESSAGE_NOT_FOUND = "message.not_found"
        const val MESSAGE_TOO_LONG = "message.too_long"
        
        // 社交相关
        const val FRIEND_REQUEST_SENT = "social.friend_request_sent"
        const val FRIEND_REQUEST_ACCEPTED = "social.friend_request_accepted"
        const val FRIEND_REQUEST_REJECTED = "social.friend_request_rejected"
        const val FRIEND_ADDED = "social.friend_added"
        const val FRIEND_REMOVED = "social.friend_removed"
        const val GROUP_CREATED = "social.group_created"
        const val GROUP_JOINED = "social.group_joined"
        const val GROUP_LEFT = "social.group_left"
        
        // 媒体相关
        const val FILE_UPLOADED = "media.file_uploaded"
        const val FILE_DELETED = "media.file_deleted"
        const val FILE_TOO_LARGE = "media.file_too_large"
        const val INVALID_FILE_TYPE = "media.invalid_file_type"
        const val UPLOAD_FAILED = "media.upload_failed"
    }
}
