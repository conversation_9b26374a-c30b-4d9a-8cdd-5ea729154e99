package com.zlim.common.utils

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinFeature
import com.fasterxml.jackson.module.kotlin.KotlinModule
import org.slf4j.LoggerFactory

/**
 * JSON工具类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
object JsonUtils {
    
    private val logger = LoggerFactory.getLogger(JsonUtils::class.java)
    
    val objectMapper: ObjectMapper = ObjectMapper().apply {
        // 注册Kotlin模块
        registerModule(
            KotlinModule.Builder()
                .withReflectionCacheSize(512)
                .configure(KotlinFeature.NullToEmptyCollection, false)
                .configure(KotlinFeature.NullToEmptyMap, false)
                .configure(KotlinFeature.NullIsSameAsDefault, false)
                .configure(KotlinFeature.SingletonSupport, false)
                .configure(KotlinFeature.StrictNullChecks, false)
                .build()
        )
        
        // 注册Java时间模块
        registerModule(JavaTimeModule())
        
        // 配置序列化特性
        configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
        configure(SerializationFeature.WRITE_DURATIONS_AS_TIMESTAMPS, false)
        configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
        configure(SerializationFeature.INDENT_OUTPUT, false)
        
        // 配置反序列化特性
        configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false)
        configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true)
        configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true)
        
        // 设置属性命名策略（可选）
        // propertyNamingStrategy = PropertyNamingStrategies.SNAKE_CASE
    }
    
    /**
     * 对象转JSON字符串
     */
    fun toJson(obj: Any?): String {
        return try {
            if (obj == null) {
                "null"
            } else {
                objectMapper.writeValueAsString(obj)
            }
        } catch (e: Exception) {
            logger.error("Failed to convert object to JSON: {}", obj, e)
            throw RuntimeException("JSON serialization failed", e)
        }
    }
    
    /**
     * 对象转JSON字节数组
     */
    fun toJsonBytes(obj: Any?): ByteArray {
        return try {
            if (obj == null) {
                "null".toByteArray()
            } else {
                objectMapper.writeValueAsBytes(obj)
            }
        } catch (e: Exception) {
            logger.error("Failed to convert object to JSON bytes: {}", obj, e)
            throw RuntimeException("JSON serialization failed", e)
        }
    }
    
    /**
     * 对象转格式化的JSON字符串
     */
    fun toPrettyJson(obj: Any?): String {
        return try {
            if (obj == null) {
                "null"
            } else {
                objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj)
            }
        } catch (e: Exception) {
            logger.error("Failed to convert object to pretty JSON: {}", obj, e)
            throw RuntimeException("JSON serialization failed", e)
        }
    }
    
    /**
     * JSON字符串转对象
     */
    inline fun <reified T> fromJson(json: String): T? {
        return fromJson(json, T::class.java)
    }
    
    /**
     * JSON字符串转对象
     */
    fun <T> fromJson(json: String, clazz: Class<T>): T? {
        return try {
            if (json.isBlank() || json == "null") {
                null
            } else {
                objectMapper.readValue(json, clazz)
            }
        } catch (e: Exception) {
            logger.error("Failed to parse JSON to object: {}", json, e)
            throw RuntimeException("JSON deserialization failed", e)
        }
    }
    
    /**
     * JSON字符串转对象（使用TypeReference）
     */
    fun <T> fromJson(json: String, typeReference: TypeReference<T>): T? {
        return try {
            if (json.isBlank() || json == "null") {
                null
            } else {
                objectMapper.readValue(json, typeReference)
            }
        } catch (e: Exception) {
            logger.error("Failed to parse JSON to object: {}", json, e)
            throw RuntimeException("JSON deserialization failed", e)
        }
    }
    
    /**
     * JSON字节数组转对象
     */
    inline fun <reified T> fromJsonBytes(bytes: ByteArray): T? {
        return fromJsonBytes(bytes, T::class.java)
    }
    
    /**
     * JSON字节数组转对象
     */
    fun <T> fromJsonBytes(bytes: ByteArray, clazz: Class<T>): T? {
        return try {
            if (bytes.isEmpty()) {
                null
            } else {
                objectMapper.readValue(bytes, clazz)
            }
        } catch (e: Exception) {
            logger.error("Failed to parse JSON bytes to object", e)
            throw RuntimeException("JSON deserialization failed", e)
        }
    }
    
    /**
     * JSON字符串转List
     */
    inline fun <reified T> fromJsonToList(json: String): List<T>? {
        return fromJson(json, object : TypeReference<List<T>>() {})
    }
    
    /**
     * JSON字符串转Map
     */
    fun fromJsonToMap(json: String): Map<String, Any>? {
        return fromJson(json, object : TypeReference<Map<String, Any>>() {})
    }
    
    /**
     * JSON字符串转Map（指定值类型）
     */
    inline fun <reified V> fromJsonToMap(json: String): Map<String, V>? {
        return fromJson(json, object : TypeReference<Map<String, V>>() {})
    }
    
    /**
     * 对象转Map
     */
    fun toMap(obj: Any): Map<String, Any> {
        return try {
            objectMapper.convertValue(obj, object : TypeReference<Map<String, Any>>() {})
        } catch (e: Exception) {
            logger.error("Failed to convert object to map: {}", obj, e)
            throw RuntimeException("Object to map conversion failed", e)
        }
    }
    
    /**
     * Map转对象
     */
    inline fun <reified T> fromMap(map: Map<String, Any>): T {
        return fromMap(map, T::class.java)
    }
    
    /**
     * Map转对象
     */
    fun <T> fromMap(map: Map<String, Any>, clazz: Class<T>): T {
        return try {
            objectMapper.convertValue(map, clazz)
        } catch (e: Exception) {
            logger.error("Failed to convert map to object: {}", map, e)
            throw RuntimeException("Map to object conversion failed", e)
        }
    }
    
    /**
     * 深拷贝对象
     */
    inline fun <reified T> deepCopy(obj: T): T {
        return deepCopy(obj, T::class.java)
    }
    
    /**
     * 深拷贝对象
     */
    fun <T> deepCopy(obj: T, clazz: Class<T>): T {
        return try {
            val json = toJson(obj)
            fromJson(json, clazz)!!
        } catch (e: Exception) {
            logger.error("Failed to deep copy object: {}", obj, e)
            throw RuntimeException("Deep copy failed", e)
        }
    }
    
    /**
     * 合并两个JSON对象
     */
    fun mergeJson(json1: String, json2: String): String {
        return try {
            val map1 = fromJsonToMap(json1) ?: emptyMap()
            val map2 = fromJsonToMap(json2) ?: emptyMap()
            val merged = map1.toMutableMap()
            merged.putAll(map2)
            toJson(merged)
        } catch (e: Exception) {
            logger.error("Failed to merge JSON: {} and {}", json1, json2, e)
            throw RuntimeException("JSON merge failed", e)
        }
    }
    
    /**
     * 检查字符串是否为有效的JSON
     */
    fun isValidJson(json: String): Boolean {
        return try {
            objectMapper.readTree(json)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取JSON字符串的大小（字节）
     */
    fun getJsonSize(obj: Any): Int {
        return toJsonBytes(obj).size
    }
    
    /**
     * 压缩JSON（移除空格和换行）
     */
    fun compactJson(json: String): String {
        return try {
            val node = objectMapper.readTree(json)
            objectMapper.writeValueAsString(node)
        } catch (e: Exception) {
            logger.error("Failed to compact JSON: {}", json, e)
            json
        }
    }
    
    /**
     * 格式化JSON（添加缩进）
     */
    fun formatJson(json: String): String {
        return try {
            val node = objectMapper.readTree(json)
            objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(node)
        } catch (e: Exception) {
            logger.error("Failed to format JSON: {}", json, e)
            json
        }
    }
}
