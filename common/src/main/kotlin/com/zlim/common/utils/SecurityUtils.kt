package com.zlim.common.utils

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.security.SecureRandom
import java.util.*
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * 安全工具类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
object SecurityUtils {
    
    private val passwordEncoder: PasswordEncoder = BCryptPasswordEncoder()
    private val secureRandom = SecureRandom()
    
    // AES加密相关常量
    private const val AES_ALGORITHM = "AES"
    private const val AES_TRANSFORMATION = "AES/GCM/NoPadding"
    private const val GCM_IV_LENGTH = 12
    private const val GCM_TAG_LENGTH = 16
    
    /**
     * 密码加密
     */
    fun encodePassword(rawPassword: String): String {
        return passwordEncoder.encode(rawPassword)
    }
    
    /**
     * 密码验证
     */
    fun matchesPassword(rawPassword: String, encodedPassword: String): Boolean {
        return passwordEncoder.matches(rawPassword, encodedPassword)
    }
    
    /**
     * 生成随机密码
     */
    fun generateRandomPassword(length: Int = 12): String {
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*"
        return (1..length)
            .map { chars.random() }
            .joinToString("")
    }
    
    /**
     * 检查密码强度
     */
    fun checkPasswordStrength(password: String): PasswordStrength {
        if (password.length < 6) return PasswordStrength.WEAK
        
        var score = 0
        
        // 长度检查
        when {
            password.length >= 12 -> score += 2
            password.length >= 8 -> score += 1
        }
        
        // 包含小写字母
        if (password.any { it.isLowerCase() }) score += 1
        
        // 包含大写字母
        if (password.any { it.isUpperCase() }) score += 1
        
        // 包含数字
        if (password.any { it.isDigit() }) score += 1
        
        // 包含特殊字符
        if (password.any { !it.isLetterOrDigit() }) score += 1
        
        return when {
            score >= 5 -> PasswordStrength.STRONG
            score >= 3 -> PasswordStrength.MEDIUM
            else -> PasswordStrength.WEAK
        }
    }
    
    /**
     * 生成随机字符串
     */
    fun generateRandomString(length: Int = 32): String {
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        return (1..length)
            .map { chars.random() }
            .joinToString("")
    }
    
    /**
     * 生成随机数字字符串
     */
    fun generateRandomNumbers(length: Int = 6): String {
        return (1..length)
            .map { secureRandom.nextInt(10) }
            .joinToString("")
    }
    
    /**
     * 生成UUID
     */
    fun generateUUID(): String {
        return UUID.randomUUID().toString()
    }
    
    /**
     * 生成短UUID（去掉横线）
     */
    fun generateShortUUID(): String {
        return UUID.randomUUID().toString().replace("-", "")
    }
    
    /**
     * MD5哈希
     */
    fun md5(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        val digest = md.digest(input.toByteArray(StandardCharsets.UTF_8))
        return digest.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * SHA-256哈希
     */
    fun sha256(input: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hash = digest.digest(input.toByteArray(StandardCharsets.UTF_8))
        return hash.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * SHA-512哈希
     */
    fun sha512(input: String): String {
        val digest = MessageDigest.getInstance("SHA-512")
        val hash = digest.digest(input.toByteArray(StandardCharsets.UTF_8))
        return hash.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * 带盐的哈希
     */
    fun hashWithSalt(input: String, salt: String): String {
        return sha256(input + salt)
    }
    
    /**
     * 生成盐值
     */
    fun generateSalt(length: Int = 16): String {
        val salt = ByteArray(length)
        secureRandom.nextBytes(salt)
        return Base64.getEncoder().encodeToString(salt)
    }
    
    /**
     * Base64编码
     */
    fun base64Encode(input: String): String {
        return Base64.getEncoder().encodeToString(input.toByteArray(StandardCharsets.UTF_8))
    }
    
    /**
     * Base64编码（字节数组）
     */
    fun base64Encode(input: ByteArray): String {
        return Base64.getEncoder().encodeToString(input)
    }
    
    /**
     * Base64解码
     */
    fun base64Decode(input: String): String {
        return String(Base64.getDecoder().decode(input), StandardCharsets.UTF_8)
    }
    
    /**
     * Base64解码（返回字节数组）
     */
    fun base64DecodeToBytes(input: String): ByteArray {
        return Base64.getDecoder().decode(input)
    }
    
    /**
     * URL安全的Base64编码
     */
    fun base64UrlEncode(input: String): String {
        return Base64.getUrlEncoder().withoutPadding().encodeToString(input.toByteArray(StandardCharsets.UTF_8))
    }
    
    /**
     * URL安全的Base64解码
     */
    fun base64UrlDecode(input: String): String {
        return String(Base64.getUrlDecoder().decode(input), StandardCharsets.UTF_8)
    }
    
    /**
     * 生成AES密钥
     */
    fun generateAESKey(): String {
        val keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM)
        keyGenerator.init(256)
        val secretKey = keyGenerator.generateKey()
        return Base64.getEncoder().encodeToString(secretKey.encoded)
    }
    
    /**
     * AES加密
     */
    fun aesEncrypt(plainText: String, key: String): String {
        val keySpec = SecretKeySpec(Base64.getDecoder().decode(key), AES_ALGORITHM)
        val cipher = Cipher.getInstance(AES_TRANSFORMATION)
        
        val iv = ByteArray(GCM_IV_LENGTH)
        secureRandom.nextBytes(iv)
        val gcmSpec = GCMParameterSpec(GCM_TAG_LENGTH * 8, iv)
        
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, gcmSpec)
        val cipherText = cipher.doFinal(plainText.toByteArray(StandardCharsets.UTF_8))
        
        val encryptedData = ByteArray(iv.size + cipherText.size)
        System.arraycopy(iv, 0, encryptedData, 0, iv.size)
        System.arraycopy(cipherText, 0, encryptedData, iv.size, cipherText.size)
        
        return Base64.getEncoder().encodeToString(encryptedData)
    }
    
    /**
     * AES解密
     */
    fun aesDecrypt(encryptedText: String, key: String): String {
        val keySpec = SecretKeySpec(Base64.getDecoder().decode(key), AES_ALGORITHM)
        val cipher = Cipher.getInstance(AES_TRANSFORMATION)
        
        val encryptedData = Base64.getDecoder().decode(encryptedText)
        val iv = ByteArray(GCM_IV_LENGTH)
        val cipherText = ByteArray(encryptedData.size - GCM_IV_LENGTH)
        
        System.arraycopy(encryptedData, 0, iv, 0, iv.size)
        System.arraycopy(encryptedData, iv.size, cipherText, 0, cipherText.size)
        
        val gcmSpec = GCMParameterSpec(GCM_TAG_LENGTH * 8, iv)
        cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmSpec)
        
        val plainText = cipher.doFinal(cipherText)
        return String(plainText, StandardCharsets.UTF_8)
    }
    
    /**
     * 生成HMAC
     */
    fun hmacSha256(data: String, key: String): String {
        val mac = javax.crypto.Mac.getInstance("HmacSHA256")
        val secretKeySpec = SecretKeySpec(key.toByteArray(StandardCharsets.UTF_8), "HmacSHA256")
        mac.init(secretKeySpec)
        val hash = mac.doFinal(data.toByteArray(StandardCharsets.UTF_8))
        return Base64.getEncoder().encodeToString(hash)
    }
    
    /**
     * 验证HMAC
     */
    fun verifyHmacSha256(data: String, key: String, expectedHmac: String): Boolean {
        val actualHmac = hmacSha256(data, key)
        return actualHmac == expectedHmac
    }
    
    /**
     * 掩码敏感信息（手机号）
     */
    fun maskPhone(phone: String): String {
        return if (phone.length >= 11) {
            phone.substring(0, 3) + "****" + phone.substring(7)
        } else {
            phone
        }
    }
    
    /**
     * 掩码敏感信息（邮箱）
     */
    fun maskEmail(email: String): String {
        val atIndex = email.indexOf('@')
        return if (atIndex > 0) {
            val username = email.substring(0, atIndex)
            val domain = email.substring(atIndex)
            val maskedUsername = if (username.length <= 2) {
                username
            } else {
                username.substring(0, 1) + "***" + username.substring(username.length - 1)
            }
            maskedUsername + domain
        } else {
            email
        }
    }
    
    /**
     * 掩码敏感信息（身份证号）
     */
    fun maskIdCard(idCard: String): String {
        return if (idCard.length >= 18) {
            idCard.substring(0, 6) + "********" + idCard.substring(14)
        } else {
            idCard
        }
    }
    
    /**
     * 密码强度枚举
     */
    enum class PasswordStrength {
        WEAK, MEDIUM, STRONG
    }
}
