package com.zlim.common.enums

/**
 * 错误码枚举
 * 按模块分类，便于维护和国际化
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
enum class ErrorCode(
    val code: String,
    val message: String,
    val httpStatus: Int = 500
) {
    
    // ========== 通用错误 1000-1999 ==========
    SUCCESS("1000", "Success", 200),
    INVALID_PARAMETER("1001", "Invalid parameter", 400),
    UNAUTHORIZED("1002", "Unauthorized", 401),
    FORBIDDEN("1003", "Forbidden", 403),
    NOT_FOUND("1004", "Resource not found", 404),
    CONFLICT("1005", "Resource conflict", 409),
    INTERNAL_ERROR("1006", "Internal server error", 500),
    SERVICE_UNAVAILABLE("1007", "Service unavailable", 503),
    RATE_LIMITED("1008", "Rate limit exceeded", 429),
    
    // ========== 认证错误 2000-2999 ==========
    INVALID_TOKEN("2001", "Invalid token", 401),
    TOKEN_EXPIRED("2002", "Token expired", 401),
    INVALID_CREDENTIALS("2003", "Invalid credentials", 401),
    ACCOUNT_LOCKED("2004", "Account locked", 423),
    ACCOUNT_DISABLED("2005", "Account disabled", 423),
    INVALID_VERIFICATION_CODE("2006", "Invalid verification code", 400),
    VERIFICATION_CODE_EXPIRED("2007", "Verification code expired", 400),
    TOO_MANY_LOGIN_ATTEMPTS("2008", "Too many login attempts", 429),
    PASSWORD_TOO_WEAK("2009", "Password too weak", 400),
    CAPTCHA_REQUIRED("2010", "Captcha required", 400),
    INVALID_CAPTCHA("2011", "Invalid captcha", 400),
    
    // ========== 用户错误 3000-3999 ==========
    USER_NOT_FOUND("3001", "User not found", 404),
    USER_ALREADY_EXISTS("3002", "User already exists", 409),
    INVALID_USERNAME("3003", "Invalid username", 400),
    INVALID_PASSWORD("3004", "Invalid password", 400),
    INVALID_EMAIL("3005", "Invalid email", 400),
    INVALID_PHONE("3006", "Invalid phone number", 400),
    EMAIL_ALREADY_EXISTS("3007", "Email already exists", 409),
    PHONE_ALREADY_EXISTS("3008", "Phone number already exists", 409),
    USER_BANNED("3009", "User is banned", 423),
    USER_DELETED("3010", "User is deleted", 410),
    PROFILE_UPDATE_FAILED("3011", "Profile update failed", 500),
    AVATAR_UPLOAD_FAILED("3012", "Avatar upload failed", 500),
    
    // ========== 消息错误 4000-4999 ==========
    MESSAGE_NOT_FOUND("4001", "Message not found", 404),
    MESSAGE_TOO_LONG("4002", "Message too long", 400),
    INVALID_MESSAGE_TYPE("4003", "Invalid message type", 400),
    MESSAGE_SEND_FAILED("4004", "Message send failed", 500),
    MESSAGE_ALREADY_READ("4005", "Message already read", 409),
    MESSAGE_RECALL_FAILED("4006", "Message recall failed", 500),
    MESSAGE_RECALL_TIMEOUT("4007", "Message recall timeout", 400),
    CONVERSATION_NOT_FOUND("4008", "Conversation not found", 404),
    CONVERSATION_ARCHIVED("4009", "Conversation is archived", 400),
    MESSAGE_DELETED("4010", "Message is deleted", 410),
    INVALID_RECIPIENT("4011", "Invalid recipient", 400),
    MESSAGE_QUOTA_EXCEEDED("4012", "Message quota exceeded", 429),
    
    // ========== 社交错误 5000-5999 ==========
    FRIEND_NOT_FOUND("5001", "Friend not found", 404),
    FRIEND_ALREADY_EXISTS("5002", "Friend already exists", 409),
    FRIEND_REQUEST_NOT_FOUND("5003", "Friend request not found", 404),
    FRIEND_REQUEST_ALREADY_SENT("5004", "Friend request already sent", 409),
    FRIEND_REQUEST_EXPIRED("5005", "Friend request expired", 410),
    CANNOT_ADD_SELF_AS_FRIEND("5006", "Cannot add self as friend", 400),
    USER_BLOCKED("5007", "User is blocked", 403),
    ALREADY_BLOCKED("5008", "User already blocked", 409),
    NOT_BLOCKED("5009", "User not blocked", 400),
    
    GROUP_NOT_FOUND("5010", "Group not found", 404),
    GROUP_FULL("5011", "Group is full", 400),
    NOT_GROUP_MEMBER("5012", "Not a group member", 403),
    ALREADY_GROUP_MEMBER("5013", "Already a group member", 409),
    INSUFFICIENT_PERMISSION("5014", "Insufficient permission", 403),
    GROUP_DISBANDED("5015", "Group is disbanded", 410),
    CANNOT_LEAVE_AS_OWNER("5016", "Cannot leave group as owner", 400),
    GROUP_INVITE_NOT_FOUND("5017", "Group invite not found", 404),
    GROUP_INVITE_EXPIRED("5018", "Group invite expired", 410),
    GROUP_NAME_TOO_LONG("5019", "Group name too long", 400),
    
    CHANNEL_NOT_FOUND("5020", "Channel not found", 404),
    ALREADY_SUBSCRIBED("5021", "Already subscribed to channel", 409),
    NOT_SUBSCRIBED("5022", "Not subscribed to channel", 400),
    CHANNEL_PRIVATE("5023", "Channel is private", 403),
    CHANNEL_DISABLED("5024", "Channel is disabled", 403),
    
    // ========== 媒体错误 6000-6999 ==========
    FILE_TOO_LARGE("6001", "File too large", 413),
    INVALID_FILE_TYPE("6002", "Invalid file type", 400),
    UPLOAD_FAILED("6003", "Upload failed", 500),
    FILE_NOT_FOUND("6004", "File not found", 404),
    FILE_CORRUPTED("6005", "File corrupted", 400),
    THUMBNAIL_GENERATION_FAILED("6006", "Thumbnail generation failed", 500),
    MEDIA_PROCESSING_FAILED("6007", "Media processing failed", 500),
    STORAGE_QUOTA_EXCEEDED("6008", "Storage quota exceeded", 413),
    INVALID_FILE_FORMAT("6009", "Invalid file format", 400),
    FILE_VIRUS_DETECTED("6010", "Virus detected in file", 400),
    CONTENT_MODERATION_FAILED("6011", "Content moderation failed", 400),
    DOWNLOAD_FAILED("6012", "Download failed", 500),
    
    // ========== 推送错误 7000-7999 ==========
    PUSH_TOKEN_INVALID("7001", "Push token invalid", 400),
    PUSH_SEND_FAILED("7002", "Push send failed", 500),
    PUSH_QUOTA_EXCEEDED("7003", "Push quota exceeded", 429),
    DEVICE_NOT_REGISTERED("7004", "Device not registered", 404),
    PUSH_DISABLED("7005", "Push notifications disabled", 400),
    INVALID_PUSH_PAYLOAD("7006", "Invalid push payload", 400),
    PUSH_PROVIDER_ERROR("7007", "Push provider error", 502),
    
    // ========== 管理错误 8000-8999 ==========
    ADMIN_PERMISSION_REQUIRED("8001", "Admin permission required", 403),
    OPERATION_NOT_ALLOWED("8002", "Operation not allowed", 403),
    AUDIT_LOG_FAILED("8003", "Audit log failed", 500),
    SYSTEM_MAINTENANCE("8004", "System under maintenance", 503),
    FEATURE_DISABLED("8005", "Feature disabled", 503),
    INVALID_ADMIN_ACTION("8006", "Invalid admin action", 400),
    
    // ========== 通知错误 9000-9999 ==========
    NOTIFICATION_NOT_FOUND("9001", "Notification not found", 404),
    NOTIFICATION_SEND_FAILED("9002", "Notification send failed", 500),
    TEMPLATE_NOT_FOUND("9003", "Template not found", 404),
    INVALID_TEMPLATE("9004", "Invalid template", 400),
    NOTIFICATION_QUOTA_EXCEEDED("9005", "Notification quota exceeded", 429),
    NOTIFICATION_DISABLED("9006", "Notifications disabled", 400);
    
    companion object {
        /**
         * 根据错误码字符串查找枚举
         */
        fun fromCode(code: String): ErrorCode? {
            return values().find { it.code == code }
        }
        
        /**
         * 获取所有错误码的映射
         */
        fun getAllCodes(): Map<String, ErrorCode> {
            return values().associateBy { it.code }
        }
        
        /**
         * 根据模块获取错误码
         */
        fun getByModule(module: String): List<ErrorCode> {
            return when (module.lowercase()) {
                "common" -> values().filter { it.code.startsWith("1") }
                "auth" -> values().filter { it.code.startsWith("2") }
                "user" -> values().filter { it.code.startsWith("3") }
                "message" -> values().filter { it.code.startsWith("4") }
                "social" -> values().filter { it.code.startsWith("5") }
                "media" -> values().filter { it.code.startsWith("6") }
                "push" -> values().filter { it.code.startsWith("7") }
                "admin" -> values().filter { it.code.startsWith("8") }
                "notification" -> values().filter { it.code.startsWith("9") }
                else -> emptyList()
            }
        }
    }
}
