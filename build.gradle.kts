import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    kotlin("jvm") version "2.0.0" apply false
    kotlin("plugin.spring") version "2.0.0" apply false
    kotlin("plugin.jpa") version "2.0.0" apply false
    id("org.springframework.boot") version "3.3.1" apply false
    id("io.spring.dependency-management") version "1.1.5" apply false
    id("com.google.protobuf") version "0.9.4" apply false
    id("org.sonarqube") version "5.0.0.4638" apply false
}

// 全局配置
allprojects {
    group = "com.zlim"
    version = "1.0.0"
    
    repositories {
        mavenCentral()
        maven("https://repo.spring.io/milestone")
        maven("https://packages.confluent.io/maven/")
    }
}

// 子项目通用配置
subprojects {
    apply(plugin = "kotlin")
    apply(plugin = "kotlin-spring")
    apply(plugin = "org.springframework.boot")
    apply(plugin = "io.spring.dependency-management")
    
    java {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }
    
    tasks.withType<KotlinCompile> {
        kotlinOptions {
            freeCompilerArgs = listOf("-Xjsr305=strict")
            jvmTarget = "21"
        }
    }
    
    tasks.withType<Test> {
        useJUnitPlatform()
    }
    
    // 版本管理
    extra["springCloudVersion"] = "2023.0.2"
    extra["springCloudAlibabaVersion"] = "2023.0.1.0"
    extra["nettyVersion"] = "4.1.111.Final"
    extra["protobufVersion"] = "4.27.2"
    extra["grpcVersion"] = "1.64.0"
    extra["mybatisPlusVersion"] = "3.5.7"
    extra["redissonVersion"] = "3.32.0"
    extra["rocketmqVersion"] = "5.3.0"
    extra["knife4jVersion"] = "4.5.0"
    extra["jjwtVersion"] = "0.12.6"
    extra["minioVersion"] = "8.5.11"
    
    dependencyManagement {
        imports {
            mavenBom("org.springframework.cloud:spring-cloud-dependencies:${property("springCloudVersion")}")
            mavenBom("com.alibaba.cloud:spring-cloud-alibaba-dependencies:${property("springCloudAlibabaVersion")}")
            mavenBom("io.grpc:grpc-bom:${property("grpcVersion")}")
        }
    }
    
    dependencies {
        implementation("org.springframework.boot:spring-boot-starter")
        implementation("org.springframework.boot:spring-boot-starter-actuator")
        implementation("org.springframework.boot:spring-boot-starter-validation")
        implementation("org.jetbrains.kotlin:kotlin-reflect")
        implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
        implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
        implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
        
        // Nacos
        implementation("com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery")
        implementation("com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config")
        
        // 监控
        implementation("io.micrometer:micrometer-registry-prometheus")
        implementation("io.opentelemetry:opentelemetry-api")
        implementation("io.opentelemetry.instrumentation:opentelemetry-spring-boot-starter")
        
        // 测试
        testImplementation("org.springframework.boot:spring-boot-starter-test")
        testImplementation("org.jetbrains.kotlin:kotlin-test-junit5")
        testImplementation("org.mockito.kotlin:mockito-kotlin:5.4.0")
        testImplementation("org.testcontainers:junit-jupiter")
        testImplementation("org.testcontainers:postgresql")
        testImplementation("org.testcontainers:kafka")
    }
}

// 根项目任务
tasks.register("clean") {
    delete(rootProject.buildDir)
}
