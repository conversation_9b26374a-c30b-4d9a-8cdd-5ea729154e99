# ZLIM - 微服务架构即时通讯系统

[![Build Status](https://github.com/zlim/zlim/workflows/CI/badge.svg)](https://github.com/zlim/zlim/actions)
[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![Java](https://img.shields.io/badge/Java-21-orange.svg)](https://openjdk.java.net/projects/jdk/21/)
[![Kotlin](https://img.shields.io/badge/Kotlin-2.0.0-purple.svg)](https://kotlinlang.org/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.3.1-green.svg)](https://spring.io/projects/spring-boot)

## 项目简介

ZLIM是一套基于微服务架构的现代化即时通讯系统，采用最新的技术栈构建，支持高并发、高可用的实时消息传输。

## 核心特性

- 🚀 **高性能**: 基于Netty的WebSocket长连接，支持百万级并发
- 🔧 **微服务架构**: 8个核心微服务，职责清晰，易于扩展
- 📱 **多端支持**: 支持iOS、Android、Web、小程序等多平台
- 🔒 **安全可靠**: JWT认证、TLS加密、权限控制
- 📊 **可观测性**: 完整的监控、日志、链路追踪体系
- 🐳 **容器化**: Docker化部署，支持Kubernetes

## 技术栈

### 核心框架
- **Spring Boot 3.3.1** - 微服务框架
- **Spring Cloud Gateway** - API网关
- **JDK 21** - 运行环境
- **Kotlin** - 开发语言
- **Gradle 8.x** - 构建工具

### 通信协议
- **Protocol Buffers** - 数据序列化
- **gRPC** - 服务间通信
- **WebSocket** - 客户端长连接
- **Netty** - 网络通信框架

### 数据存储
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **MinIO** - 对象存储

### 消息队列
- **Apache RocketMQ** - 消息队列

### 服务治理
- **Nacos** - 服务注册发现和配置管理
- **Sentinel** - 流量控制和熔断降级

### 监控运维
- **Prometheus** - 指标监控
- **Grafana** - 监控面板
- **ELK Stack** - 日志分析
- **Jaeger** - 链路追踪
- **Docker** - 容器化部署

## 系统架构

### 微服务列表

| 服务名称 | 端口 | 描述 |
|---------|------|------|
| gateway-service | 8080 | Netty WebSocket网关服务 |
| user-service | 8081 | 用户管理服务 |
| auth-service | 8082 | 认证授权服务 |
| message-service | 8083 | 消息核心服务 |
| social-service | 8084 | 社交关系服务 |
| media-service | 8085 | 媒体文件服务 |
| push-service | 8086 | 消息推送服务 |
| admin-service | 8087 | 管理后台服务 |
| notification-service | 8088 | 通知服务 |

### 架构图

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │    │   Client    │    │   Client    │
│ (iOS/Android│    │    (Web)    │    │ (小程序)     │
└─────────────┘    └─────────────┘    └─────────────┘
        │                  │                  │
        └──────────────────┼──────────────────┘
                           │
                    ┌─────────────┐
                    │   Gateway   │
                    │   Service   │
                    └─────────────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    User     │    │   Message   │    │   Social    │
│   Service   │    │   Service   │    │   Service   │
└─────────────┘    └─────────────┘    └─────────────┘
        │                  │                  │
        └──────────────────┼──────────────────┘
                           │
                    ┌─────────────┐
                    │  Database   │
                    │   & Cache   │
                    └─────────────┘
```

## 快速开始

### 环境要求

- JDK 21+
- Docker & Docker Compose
- Gradle 8.x

### 本地开发

1. **克隆项目**
```bash
git clone https://github.com/zlim/zlim.git
cd zlim
```

2. **启动基础设施**
```bash
docker-compose -f docker/dev/docker-compose.yml up -d
```

3. **构建项目**
```bash
./gradlew build
```

4. **启动服务**
```bash
# 启动所有服务
./gradlew bootRun --parallel
```

### Docker部署

```bash
# 构建镜像
docker-compose -f docker/prod/docker-compose.yml build

# 启动服务
docker-compose -f docker/prod/docker-compose.yml up -d
```

## API文档

启动服务后，访问以下地址查看API文档：

- **Gateway Service**: http://localhost:8080/doc.html
- **User Service**: http://localhost:8081/doc.html
- **Auth Service**: http://localhost:8082/doc.html

## 监控面板

- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Jaeger**: http://localhost:16686
- **Kibana**: http://localhost:5601

## 开发指南

### 代码规范

- 使用Kotlin作为主要开发语言
- 遵循Spring Boot最佳实践
- 使用Protocol Buffers定义API接口
- 统一的错误处理和国际化支持

### 测试

```bash
# 运行单元测试
./gradlew test

# 运行集成测试
./gradlew integrationTest

# 生成测试报告
./gradlew jacocoTestReport
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 Apache 2.0 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系我们

- 项目主页: https://github.com/zlim/zlim
- 问题反馈: https://github.com/zlim/zlim/issues
- 邮箱: <EMAIL>
