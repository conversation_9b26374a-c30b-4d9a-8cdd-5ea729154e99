-- ========================================
-- 媒体服务数据库表结构
-- ========================================

-- 创建媒体数据库
CREATE DATABASE IF NOT EXISTS zlim_media;
USE zlim_media;

-- 媒体文件表
CREATE TABLE media_files (
    id VARCHAR(64) PRIMARY KEY, -- 文件ID
    filename VARCHAR(500) NOT NULL,
    original_filename VARCHAR(500) NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    media_type SMALLINT NOT NULL, -- 1:图片, 2:音频, 3:视频, 4:文档, 5:压缩包, 6:其他
    purpose SMALLINT NOT NULL, -- 1:头像, 2:消息, 3:群头像, 4:频道头像, 5:背景, 6:附件
    uploader_id BIGINT NOT NULL,
    storage_provider SMALLINT DEFAULT 1, -- 1:MinIO, 2:阿里云OSS, 3:腾讯云COS, 4:AWS S3
    storage_path VARCHAR(1000) NOT NULL,
    storage_bucket VARCHAR(200),
    public_url VARCHAR(1000),
    thumbnail_url VARCHAR(1000),
    preview_url VARCHAR(1000),
    download_count BIGINT DEFAULT 0,
    status SMALLINT DEFAULT 1, -- 1:上传中, 2:处理中, 3:可用, 4:失败, 5:已删除, 6:已隔离
    checksum VARCHAR(64), -- 文件校验和
    encryption_key VARCHAR(100), -- 加密密钥（如果加密）
    expires_at TIMESTAMP WITH TIME ZONE, -- 过期时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    INDEX idx_media_files_uploader_id (uploader_id),
    INDEX idx_media_files_media_type (media_type),
    INDEX idx_media_files_purpose (purpose),
    INDEX idx_media_files_status (status),
    INDEX idx_media_files_storage_provider (storage_provider),
    INDEX idx_media_files_checksum (checksum),
    INDEX idx_media_files_created_at (created_at),
    INDEX idx_media_files_expires_at (expires_at),
    INDEX idx_media_files_deleted_at (deleted_at)
);

-- 媒体元数据表
CREATE TABLE media_metadata (
    id BIGSERIAL PRIMARY KEY,
    file_id VARCHAR(64) NOT NULL,
    
    -- 通用元数据
    width INT,
    height INT,
    duration INT, -- 时长（秒）
    bitrate INT, -- 比特率
    format VARCHAR(50), -- 格式
    codec VARCHAR(50), -- 编解码器
    
    -- 图片元数据
    color_space VARCHAR(50),
    dpi INT,
    has_transparency BOOLEAN,
    color_count INT,
    dominant_colors TEXT[], -- 主要颜色
    
    -- 音频元数据
    title VARCHAR(200),
    artist VARCHAR(200),
    album VARCHAR(200),
    genre VARCHAR(100),
    year INT,
    track_number INT,
    sample_rate INT,
    channels INT,
    
    -- 视频元数据
    frame_rate DECIMAL(5,2),
    aspect_ratio VARCHAR(20),
    has_audio BOOLEAN,
    video_streams JSONB, -- 视频流信息
    
    -- EXIF数据
    exif_data JSONB,
    
    -- 自定义元数据
    custom_metadata JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    UNIQUE KEY uk_media_metadata_file_id (file_id)
);

-- 上传会话表
CREATE TABLE upload_sessions (
    id VARCHAR(64) PRIMARY KEY, -- 上传会话ID
    user_id BIGINT NOT NULL,
    filename VARCHAR(500) NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    media_type SMALLINT NOT NULL,
    purpose SMALLINT NOT NULL,
    storage_provider SMALLINT DEFAULT 1,
    storage_path VARCHAR(1000),
    storage_bucket VARCHAR(200),
    upload_url VARCHAR(1000),
    upload_method SMALLINT DEFAULT 1, -- 1:直传, 2:分片上传, 3:断点续传
    chunk_size BIGINT, -- 分片大小
    total_chunks INT, -- 总分片数
    uploaded_chunks INT DEFAULT 0, -- 已上传分片数
    upload_credentials JSONB, -- 上传凭证
    status SMALLINT DEFAULT 1, -- 1:初始化, 2:上传中, 3:已完成, 4:已失败, 5:已取消
    error_message TEXT,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '1 hour'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_upload_sessions_user_id (user_id),
    INDEX idx_upload_sessions_status (status),
    INDEX idx_upload_sessions_expires_at (expires_at),
    INDEX idx_upload_sessions_created_at (created_at)
);

-- 上传分片表
CREATE TABLE upload_chunks (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(64) NOT NULL,
    chunk_number INT NOT NULL,
    chunk_size BIGINT NOT NULL,
    checksum VARCHAR(64),
    upload_url VARCHAR(1000),
    status SMALLINT DEFAULT 1, -- 1:待上传, 2:上传中, 3:已完成, 4:已失败
    retry_count INT DEFAULT 0,
    error_message TEXT,
    uploaded_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES upload_sessions(id) ON DELETE CASCADE,
    UNIQUE KEY uk_upload_chunks_session_chunk (session_id, chunk_number),
    INDEX idx_upload_chunks_session_id (session_id),
    INDEX idx_upload_chunks_status (status)
);

-- 内容审核表
CREATE TABLE content_moderation (
    id BIGSERIAL PRIMARY KEY,
    file_id VARCHAR(64) NOT NULL,
    provider SMALLINT NOT NULL, -- 1:内容安全, 2:成人内容, 3:暴力内容, 4:垃圾内容, 5:人脸检测, 6:文字识别
    is_safe BOOLEAN NOT NULL,
    confidence DECIMAL(5,4), -- 置信度
    labels JSONB, -- 审核标签
    reason TEXT,
    raw_result JSONB, -- 原始审核结果
    moderated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    INDEX idx_content_moderation_file_id (file_id),
    INDEX idx_content_moderation_provider (provider),
    INDEX idx_content_moderation_is_safe (is_safe),
    INDEX idx_content_moderation_moderated_at (moderated_at)
);

-- 媒体转换任务表
CREATE TABLE media_conversion_tasks (
    id VARCHAR(64) PRIMARY KEY, -- 任务ID
    source_file_id VARCHAR(64) NOT NULL,
    target_file_id VARCHAR(64),
    conversion_type SMALLINT NOT NULL, -- 1:格式转换, 2:压缩, 3:缩略图生成, 4:水印添加
    source_format VARCHAR(50),
    target_format VARCHAR(50),
    conversion_options JSONB, -- 转换参数
    status SMALLINT DEFAULT 1, -- 1:待处理, 2:处理中, 3:已完成, 4:已失败, 5:已取消
    progress INT DEFAULT 0, -- 进度百分比
    error_message TEXT,
    worker_id VARCHAR(100), -- 处理节点ID
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (source_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    FOREIGN KEY (target_file_id) REFERENCES media_files(id) ON DELETE SET NULL,
    INDEX idx_media_conversion_tasks_source_file_id (source_file_id),
    INDEX idx_media_conversion_tasks_target_file_id (target_file_id),
    INDEX idx_media_conversion_tasks_status (status),
    INDEX idx_media_conversion_tasks_conversion_type (conversion_type),
    INDEX idx_media_conversion_tasks_worker_id (worker_id),
    INDEX idx_media_conversion_tasks_created_at (created_at)
);

-- 媒体访问日志表
CREATE TABLE media_access_logs (
    id BIGSERIAL PRIMARY KEY,
    file_id VARCHAR(64) NOT NULL,
    user_id BIGINT,
    access_type SMALLINT NOT NULL, -- 1:查看, 2:下载, 3:分享, 4:删除
    ip_address INET,
    user_agent TEXT,
    referer VARCHAR(1000),
    response_code INT,
    response_size BIGINT,
    response_time INT, -- 响应时间（毫秒）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_media_access_logs_file_id (file_id),
    INDEX idx_media_access_logs_user_id (user_id),
    INDEX idx_media_access_logs_access_type (access_type),
    INDEX idx_media_access_logs_ip_address (ip_address),
    INDEX idx_media_access_logs_created_at (created_at)
);

-- 媒体统计表
CREATE TABLE media_stats (
    id BIGSERIAL PRIMARY KEY,
    date DATE NOT NULL,
    total_files BIGINT DEFAULT 0,
    total_size BIGINT DEFAULT 0, -- 总大小（字节）
    image_count BIGINT DEFAULT 0,
    audio_count BIGINT DEFAULT 0,
    video_count BIGINT DEFAULT 0,
    document_count BIGINT DEFAULT 0,
    other_count BIGINT DEFAULT 0,
    upload_count BIGINT DEFAULT 0, -- 当日上传数
    download_count BIGINT DEFAULT 0, -- 当日下载数
    delete_count BIGINT DEFAULT 0, -- 当日删除数
    storage_cost DECIMAL(10,2) DEFAULT 0, -- 存储成本
    bandwidth_cost DECIMAL(10,2) DEFAULT 0, -- 带宽成本
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_media_stats_date (date),
    INDEX idx_media_stats_date (date)
);

-- 存储配额表
CREATE TABLE storage_quotas (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    quota_type SMALLINT DEFAULT 1, -- 1:个人配额, 2:群组配额, 3:频道配额
    total_quota BIGINT NOT NULL, -- 总配额（字节）
    used_quota BIGINT DEFAULT 0, -- 已使用配额（字节）
    file_count BIGINT DEFAULT 0, -- 文件数量
    max_file_size BIGINT, -- 单文件最大大小
    allowed_types TEXT[], -- 允许的文件类型
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_storage_quotas_user_type (user_id, quota_type),
    INDEX idx_storage_quotas_user_id (user_id),
    INDEX idx_storage_quotas_quota_type (quota_type),
    INDEX idx_storage_quotas_expires_at (expires_at)
);

-- 创建更新时间触发器
CREATE TRIGGER update_media_files_updated_at BEFORE UPDATE ON media_files
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_media_metadata_updated_at BEFORE UPDATE ON media_metadata
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_upload_sessions_updated_at BEFORE UPDATE ON upload_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_media_conversion_tasks_updated_at BEFORE UPDATE ON media_conversion_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_media_stats_updated_at BEFORE UPDATE ON media_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_storage_quotas_updated_at BEFORE UPDATE ON storage_quotas
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建存储配额更新触发器函数
CREATE OR REPLACE FUNCTION update_storage_quota()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- 文件上传时增加配额使用量
        UPDATE storage_quotas 
        SET used_quota = used_quota + NEW.file_size,
            file_count = file_count + 1
        WHERE user_id = NEW.uploader_id AND quota_type = 1;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- 文件删除时减少配额使用量
        UPDATE storage_quotas 
        SET used_quota = used_quota - OLD.file_size,
            file_count = file_count - 1
        WHERE user_id = OLD.uploader_id AND quota_type = 1;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        -- 文件状态变更时处理配额
        IF OLD.status != 5 AND NEW.status = 5 THEN
            -- 文件被删除
            UPDATE storage_quotas 
            SET used_quota = used_quota - NEW.file_size,
                file_count = file_count - 1
            WHERE user_id = NEW.uploader_id AND quota_type = 1;
        ELSIF OLD.status = 5 AND NEW.status != 5 THEN
            -- 文件恢复
            UPDATE storage_quotas 
            SET used_quota = used_quota + NEW.file_size,
                file_count = file_count + 1
            WHERE user_id = NEW.uploader_id AND quota_type = 1;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 为媒体文件表添加配额更新触发器
CREATE TRIGGER update_storage_quota_trigger
    AFTER INSERT OR UPDATE OR DELETE ON media_files
    FOR EACH ROW EXECUTE FUNCTION update_storage_quota();

-- 创建文件清理函数（清理过期文件）
CREATE OR REPLACE FUNCTION cleanup_expired_files()
RETURNS void AS $$
BEGIN
    -- 标记过期文件为已删除
    UPDATE media_files 
    SET status = 5, deleted_at = CURRENT_TIMESTAMP
    WHERE expires_at < CURRENT_TIMESTAMP AND status != 5;
    
    -- 清理过期的上传会话
    DELETE FROM upload_sessions 
    WHERE expires_at < CURRENT_TIMESTAMP AND status IN (1, 4, 5);
END;
$$ LANGUAGE plpgsql;

-- 创建定时任务来清理过期文件（需要pg_cron扩展）
-- SELECT cron.schedule('cleanup-expired-files', '0 2 * * *', 'SELECT cleanup_expired_files();');

-- 插入默认存储配额
INSERT INTO storage_quotas (user_id, quota_type, total_quota, max_file_size, allowed_types) VALUES
(1, 1, 10737418240, 104857600, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'audio/mp3', 'application/pdf']), -- 10GB配额，100MB单文件限制
(2, 1, 10737418240, 104857600, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'audio/mp3', 'application/pdf']);
