-- ========================================
-- 用户服务数据库表结构
-- ========================================

-- 创建用户数据库
CREATE DATABASE IF NOT EXISTS zlim_user;
USE zlim_user;

-- 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(32) NOT NULL,
    nickname VARCHAR(100),
    bio TEXT,
    avatar VARCHAR(500),
    gender SMALLINT DEFAULT 0, -- 0:未指定, 1:男, 2:女, 3:其他, 4:不愿透露
    birthday DATE,
    location_latitude DECIMAL(10, 8),
    location_longitude DECIMAL(11, 8),
    location_address VARCHAR(500),
    location_city VARCHAR(100),
    location_country VARCHAR(100),
    status SMALLINT DEFAULT 1, -- 1:激活, 2:未激活, 3:封禁, 4:删除
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    last_login_ip INET,
    login_count BIGINT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- 索引
    INDEX idx_users_username (username),
    INDEX idx_users_email (email),
    INDEX idx_users_phone (phone),
    INDEX idx_users_status (status),
    INDEX idx_users_created_at (created_at),
    INDEX idx_users_deleted_at (deleted_at)
);

-- 用户设置表
CREATE TABLE user_settings (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    
    -- 隐私设置
    show_phone BOOLEAN DEFAULT FALSE,
    show_email BOOLEAN DEFAULT FALSE,
    show_location BOOLEAN DEFAULT FALSE,
    allow_friend_requests BOOLEAN DEFAULT TRUE,
    allow_group_invites BOOLEAN DEFAULT TRUE,
    show_online_status BOOLEAN DEFAULT TRUE,
    show_last_seen BOOLEAN DEFAULT TRUE,
    friend_request_mode SMALLINT DEFAULT 1, -- 1:所有人, 2:朋友的朋友, 3:无人
    
    -- 通知设置
    push_enabled BOOLEAN DEFAULT TRUE,
    message_notifications BOOLEAN DEFAULT TRUE,
    friend_request_notifications BOOLEAN DEFAULT TRUE,
    group_notifications BOOLEAN DEFAULT TRUE,
    system_notifications BOOLEAN DEFAULT TRUE,
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    quiet_days SMALLINT[], -- 0=周日, 1=周一, ...
    
    -- 聊天设置
    read_receipts BOOLEAN DEFAULT TRUE,
    typing_indicators BOOLEAN DEFAULT TRUE,
    auto_download_images BOOLEAN DEFAULT TRUE,
    auto_download_videos BOOLEAN DEFAULT FALSE,
    auto_download_files BOOLEAN DEFAULT FALSE,
    font_size VARCHAR(20) DEFAULT 'medium',
    sound_enabled BOOLEAN DEFAULT TRUE,
    vibration_enabled BOOLEAN DEFAULT TRUE,
    
    -- 系统设置
    language VARCHAR(10) DEFAULT 'zh_CN',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
    theme VARCHAR(20) DEFAULT 'light',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_settings_user_id (user_id)
);

-- 用户统计表
CREATE TABLE user_stats (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    friends_count BIGINT DEFAULT 0,
    groups_count BIGINT DEFAULT 0,
    channels_count BIGINT DEFAULT 0,
    messages_sent BIGINT DEFAULT 0,
    messages_received BIGINT DEFAULT 0,
    files_shared BIGINT DEFAULT 0,
    total_online_time BIGINT DEFAULT 0, -- 总在线时间（秒）
    last_active TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_stats_user_id (user_id)
);

-- 用户设备表
CREATE TABLE user_devices (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    device_id VARCHAR(100) NOT NULL,
    device_type SMALLINT NOT NULL, -- 1:iOS, 2:Android, 3:Web, 4:Desktop, 5:小程序
    device_model VARCHAR(100),
    os_version VARCHAR(50),
    app_version VARCHAR(50),
    push_token VARCHAR(500),
    push_provider SMALLINT, -- 1:FCM, 2:APNS, 3:极光, 4:友盟, 5:个推, 6:华为, 7:小米, 8:OPPO, 9:VIVO, 10:魅族
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_active TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_devices_user_device (user_id, device_id),
    INDEX idx_user_devices_user_id (user_id),
    INDEX idx_user_devices_device_id (device_id),
    INDEX idx_user_devices_push_token (push_token),
    INDEX idx_user_devices_last_active (last_active)
);

-- 用户会话表
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    device_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    access_token_hash VARCHAR(255),
    refresh_token_hash VARCHAR(255),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_active TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_sessions_user_id (user_id),
    INDEX idx_user_sessions_session_id (session_id),
    INDEX idx_user_sessions_expires_at (expires_at),
    INDEX idx_user_sessions_last_active (last_active)
);

-- 用户登录日志表
CREATE TABLE user_login_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT,
    username VARCHAR(50),
    login_type SMALLINT NOT NULL, -- 1:密码, 2:验证码, 3:第三方
    device_id VARCHAR(100),
    device_type SMALLINT,
    ip_address INET,
    user_agent TEXT,
    location_country VARCHAR(100),
    location_city VARCHAR(100),
    status SMALLINT NOT NULL, -- 1:成功, 2:失败, 3:异常
    failure_reason VARCHAR(200),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_login_logs_user_id (user_id),
    INDEX idx_user_login_logs_username (username),
    INDEX idx_user_login_logs_ip_address (ip_address),
    INDEX idx_user_login_logs_created_at (created_at),
    INDEX idx_user_login_logs_status (status)
);

-- 用户标签表
CREATE TABLE user_tags (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    tag VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_tags_user_tag (user_id, tag),
    INDEX idx_user_tags_tag (tag)
);

-- 用户自定义字段表
CREATE TABLE user_custom_fields (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    field_key VARCHAR(100) NOT NULL,
    field_value TEXT,
    field_type SMALLINT DEFAULT 1, -- 1:字符串, 2:数字, 3:布尔, 4:日期, 5:JSON
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_custom_fields_user_key (user_id, field_key),
    INDEX idx_user_custom_fields_key (field_key)
);

-- 用户封禁记录表
CREATE TABLE user_ban_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    operator_id BIGINT,
    ban_type SMALLINT NOT NULL, -- 1:临时封禁, 2:永久封禁, 3:功能限制
    reason TEXT NOT NULL,
    ban_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (operator_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_ban_records_user_id (user_id),
    INDEX idx_user_ban_records_operator_id (operator_id),
    INDEX idx_user_ban_records_ban_until (ban_until),
    INDEX idx_user_ban_records_is_active (is_active)
);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_settings_updated_at BEFORE UPDATE ON user_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_stats_updated_at BEFORE UPDATE ON user_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_devices_updated_at BEFORE UPDATE ON user_devices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_custom_fields_updated_at BEFORE UPDATE ON user_custom_fields
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_ban_records_updated_at BEFORE UPDATE ON user_ban_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认数据
INSERT INTO users (username, email, password_hash, salt, nickname, status) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLyEbeZTOWxS', 'admin_salt', '系统管理员', 1),
('system', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLyEbeZTOWxS', 'system_salt', '系统用户', 1);

-- 为默认用户插入设置和统计
INSERT INTO user_settings (user_id) VALUES (1), (2);
INSERT INTO user_stats (user_id) VALUES (1), (2);
