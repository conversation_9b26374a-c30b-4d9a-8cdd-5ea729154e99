-- ========================================
-- 消息服务数据库表结构
-- ========================================

-- 创建消息数据库
CREATE DATABASE IF NOT EXISTS zlim_message;
USE zlim_message;

-- 会话表
CREATE TABLE conversations (
    id VARCHAR(64) PRIMARY KEY, -- 会话ID，格式：private_{smaller_uid}_{larger_uid} 或 group_{group_id}
    type SMALLINT NOT NULL, -- 1:私聊, 2:群聊, 3:频道, 4:系统会话
    name VARCHAR(200),
    avatar VARCHAR(500),
    description TEXT,
    participant_count INT DEFAULT 0,
    last_message_id VARCHAR(64),
    last_message_time TIMESTAMP WITH TIME ZONE,
    last_message_content TEXT,
    last_message_type SMALLINT,
    is_archived <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
    is_muted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_conversations_type (type),
    INDEX idx_conversations_last_message_time (last_message_time),
    INDEX idx_conversations_created_at (created_at)
);

-- 会话参与者表
CREATE TABLE conversation_participants (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(64) NOT NULL,
    user_id BIGINT NOT NULL,
    role SMALLINT DEFAULT 1, -- 1:普通成员, 2:管理员, 3:所有者
    nickname VARCHAR(100), -- 在此会话中的昵称
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_read_message_id VARCHAR(64),
    last_read_time TIMESTAMP WITH TIME ZONE,
    unread_count BIGINT DEFAULT 0,
    is_muted BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    left_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    UNIQUE KEY uk_conversation_participants (conversation_id, user_id),
    INDEX idx_conversation_participants_user_id (user_id),
    INDEX idx_conversation_participants_conversation_id (conversation_id),
    INDEX idx_conversation_participants_last_read_time (last_read_time)
);

-- 消息表（按月分表）
CREATE TABLE messages (
    id VARCHAR(64) PRIMARY KEY, -- 消息ID，格式：{sender_id}_{timestamp_ms}_{random}
    conversation_id VARCHAR(64) NOT NULL,
    sender_id BIGINT NOT NULL,
    client_msg_id VARCHAR(100), -- 客户端消息ID，用于去重
    type SMALLINT NOT NULL, -- 1:文本, 2:图片, 3:音频, 4:视频, 5:文件, 6:位置, 7:联系人, 8:链接, 9:系统, 10:自定义
    content JSONB NOT NULL, -- 消息内容，JSON格式
    status SMALLINT DEFAULT 1, -- 1:发送中, 2:已发送, 3:已送达, 4:已读, 5:发送失败
    priority SMALLINT DEFAULT 2, -- 1:低, 2:普通, 3:高, 4:紧急
    reply_to_message_id VARCHAR(64), -- 回复的消息ID
    forward_from_message_ids TEXT[], -- 转发的原消息ID列表
    is_recalled BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    recalled_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    scheduled_time TIMESTAMP WITH TIME ZONE, -- 定时发送时间
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    INDEX idx_messages_conversation_id (conversation_id),
    INDEX idx_messages_sender_id (sender_id),
    INDEX idx_messages_created_at (created_at),
    INDEX idx_messages_type (type),
    INDEX idx_messages_status (status),
    INDEX idx_messages_client_msg_id (client_msg_id),
    INDEX idx_messages_reply_to (reply_to_message_id),
    INDEX idx_messages_scheduled_time (scheduled_time)
) PARTITION BY RANGE (created_at);

-- 创建消息表分区（按月）
CREATE TABLE messages_202501 PARTITION OF messages
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE messages_202502 PARTITION OF messages
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

CREATE TABLE messages_202503 PARTITION OF messages
    FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');

CREATE TABLE messages_202504 PARTITION OF messages
    FOR VALUES FROM ('2025-04-01') TO ('2025-05-01');

CREATE TABLE messages_202505 PARTITION OF messages
    FOR VALUES FROM ('2025-05-01') TO ('2025-06-01');

CREATE TABLE messages_202506 PARTITION OF messages
    FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');

CREATE TABLE messages_202507 PARTITION OF messages
    FOR VALUES FROM ('2025-07-01') TO ('2025-08-01');

CREATE TABLE messages_202508 PARTITION OF messages
    FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');

CREATE TABLE messages_202509 PARTITION OF messages
    FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');

CREATE TABLE messages_202510 PARTITION OF messages
    FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');

CREATE TABLE messages_202511 PARTITION OF messages
    FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');

CREATE TABLE messages_202512 PARTITION OF messages
    FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');

-- 消息回执表
CREATE TABLE message_receipts (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(64) NOT NULL,
    user_id BIGINT NOT NULL,
    type SMALLINT NOT NULL, -- 1:已送达, 2:已读
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_message_receipts (message_id, user_id, type),
    INDEX idx_message_receipts_message_id (message_id),
    INDEX idx_message_receipts_user_id (user_id),
    INDEX idx_message_receipts_created_at (created_at)
);

-- 消息提及表
CREATE TABLE message_mentions (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(64) NOT NULL,
    user_id BIGINT NOT NULL,
    start_pos INT NOT NULL,
    length INT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_message_mentions_message_id (message_id),
    INDEX idx_message_mentions_user_id (user_id)
);

-- 消息反应表（表情回应）
CREATE TABLE message_reactions (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(64) NOT NULL,
    user_id BIGINT NOT NULL,
    emoji_code VARCHAR(50) NOT NULL,
    emoji_unicode VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_message_reactions (message_id, user_id, emoji_code),
    INDEX idx_message_reactions_message_id (message_id),
    INDEX idx_message_reactions_user_id (user_id)
);

-- 消息搜索索引表
CREATE TABLE message_search_index (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(64) NOT NULL,
    conversation_id VARCHAR(64) NOT NULL,
    sender_id BIGINT NOT NULL,
    content_text TEXT NOT NULL,
    search_vector tsvector,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_message_search_index_message_id (message_id),
    INDEX idx_message_search_index_conversation_id (conversation_id),
    INDEX idx_message_search_index_sender_id (sender_id),
    INDEX idx_message_search_index_search_vector USING gin(search_vector)
);

-- 消息统计表
CREATE TABLE message_stats (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(64) NOT NULL,
    date DATE NOT NULL,
    message_count BIGINT DEFAULT 0,
    text_count BIGINT DEFAULT 0,
    image_count BIGINT DEFAULT 0,
    audio_count BIGINT DEFAULT 0,
    video_count BIGINT DEFAULT 0,
    file_count BIGINT DEFAULT 0,
    total_size BIGINT DEFAULT 0, -- 总大小（字节）
    active_users BIGINT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_message_stats_conversation_date (conversation_id, date),
    INDEX idx_message_stats_date (date),
    INDEX idx_message_stats_conversation_id (conversation_id)
);

-- 草稿消息表
CREATE TABLE draft_messages (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(64) NOT NULL,
    user_id BIGINT NOT NULL,
    content JSONB NOT NULL,
    type SMALLINT NOT NULL,
    reply_to_message_id VARCHAR(64),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_draft_messages_conversation_user (conversation_id, user_id),
    INDEX idx_draft_messages_user_id (user_id),
    INDEX idx_draft_messages_updated_at (updated_at)
);

-- 定时消息表
CREATE TABLE scheduled_messages (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(64) UNIQUE NOT NULL,
    conversation_id VARCHAR(64) NOT NULL,
    sender_id BIGINT NOT NULL,
    content JSONB NOT NULL,
    type SMALLINT NOT NULL,
    scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
    status SMALLINT DEFAULT 1, -- 1:待发送, 2:已发送, 3:已取消, 4:发送失败
    retry_count INT DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_scheduled_messages_scheduled_time (scheduled_time),
    INDEX idx_scheduled_messages_sender_id (sender_id),
    INDEX idx_scheduled_messages_status (status)
);

-- 创建更新时间触发器
CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_message_stats_updated_at BEFORE UPDATE ON message_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_draft_messages_updated_at BEFORE UPDATE ON draft_messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scheduled_messages_updated_at BEFORE UPDATE ON scheduled_messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建全文搜索触发器函数
CREATE OR REPLACE FUNCTION update_message_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('simple', NEW.content_text);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为消息搜索索引表添加全文搜索触发器
CREATE TRIGGER update_message_search_vector_trigger
    BEFORE INSERT OR UPDATE ON message_search_index
    FOR EACH ROW EXECUTE FUNCTION update_message_search_vector();

-- 创建自动分区函数
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name text, start_date date)
RETURNS void AS $$
DECLARE
    partition_name text;
    end_date date;
BEGIN
    partition_name := table_name || '_' || to_char(start_date, 'YYYYMM');
    end_date := start_date + interval '1 month';
    
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF %I FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;

-- 创建定时任务来自动创建分区（需要pg_cron扩展）
-- SELECT cron.schedule('create-partitions', '0 0 1 * *', 'SELECT create_monthly_partition(''messages'', date_trunc(''month'', CURRENT_DATE + interval ''1 month''));');
