-- ========================================
-- 管理服务数据库表结构
-- ========================================

-- 创建管理数据库
CREATE DATABASE IF NOT EXISTS zlim_admin;
USE zlim_admin;

-- 管理员表
CREATE TABLE admins (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 关联用户表
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role SMALLINT NOT NULL, -- 1:超级管理员, 2:系统管理员, 3:内容管理员, 4:客服管理员
    permissions TEXT[], -- 权限列表
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    last_login_ip INET,
    created_by BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_admins_user_id (user_id),
    INDEX idx_admins_username (username),
    INDEX idx_admins_email (email),
    INDEX idx_admins_role (role),
    INDEX idx_admins_is_active (is_active),
    INDEX idx_admins_created_by (created_by)
);

-- 审计日志表
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    admin_id BIGINT,
    user_id BIGINT, -- 被操作的用户ID
    action VARCHAR(100) NOT NULL, -- 操作类型
    resource VARCHAR(100) NOT NULL, -- 资源类型
    resource_id VARCHAR(100), -- 资源ID
    old_values JSONB, -- 操作前的值
    new_values JSONB, -- 操作后的值
    details JSONB, -- 详细信息
    ip_address INET,
    user_agent TEXT,
    result SMALLINT DEFAULT 1, -- 1:成功, 2:失败
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_audit_logs_admin_id (admin_id),
    INDEX idx_audit_logs_user_id (user_id),
    INDEX idx_audit_logs_action (action),
    INDEX idx_audit_logs_resource (resource),
    INDEX idx_audit_logs_resource_id (resource_id),
    INDEX idx_audit_logs_ip_address (ip_address),
    INDEX idx_audit_logs_result (result),
    INDEX idx_audit_logs_created_at (created_at)
);

-- 系统配置表
CREATE TABLE system_configs (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_type SMALLINT DEFAULT 1, -- 1:字符串, 2:数字, 3:布尔, 4:JSON, 5:加密
    category VARCHAR(50), -- 配置分类
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- 是否公开（客户端可访问）
    is_encrypted BOOLEAN DEFAULT FALSE, -- 是否加密存储
    created_by BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_system_configs_category (category),
    INDEX idx_system_configs_is_public (is_public),
    INDEX idx_system_configs_created_by (created_by)
);

-- 系统通知表
CREATE TABLE system_notifications (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    type SMALLINT NOT NULL, -- 1:系统公告, 2:维护通知, 3:功能更新, 4:紧急通知, 5:营销消息
    priority SMALLINT DEFAULT 2, -- 1:低, 2:普通, 3:高, 4:紧急
    target_type SMALLINT DEFAULT 1, -- 1:所有用户, 2:指定用户, 3:用户组, 4:设备类型
    target_users BIGINT[], -- 目标用户ID列表
    target_groups TEXT[], -- 目标用户组
    target_devices SMALLINT[], -- 目标设备类型
    status SMALLINT DEFAULT 1, -- 1:草稿, 2:已发布, 3:已撤回
    send_immediately BOOLEAN DEFAULT FALSE,
    scheduled_time TIMESTAMP WITH TIME ZONE,
    sent_count BIGINT DEFAULT 0,
    read_count BIGINT DEFAULT 0,
    created_by BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    published_at TIMESTAMP WITH TIME ZONE,
    
    INDEX idx_system_notifications_type (type),
    INDEX idx_system_notifications_priority (priority),
    INDEX idx_system_notifications_target_type (target_type),
    INDEX idx_system_notifications_status (status),
    INDEX idx_system_notifications_scheduled_time (scheduled_time),
    INDEX idx_system_notifications_created_by (created_by),
    INDEX idx_system_notifications_published_at (published_at)
);

-- 用户通知记录表
CREATE TABLE user_notification_records (
    id BIGSERIAL PRIMARY KEY,
    notification_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    status SMALLINT DEFAULT 1, -- 1:已发送, 2:已读, 3:已删除
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (notification_id) REFERENCES system_notifications(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_notification_records (notification_id, user_id),
    INDEX idx_user_notification_records_notification_id (notification_id),
    INDEX idx_user_notification_records_user_id (user_id),
    INDEX idx_user_notification_records_status (status),
    INDEX idx_user_notification_records_sent_at (sent_at)
);

-- 内容审核规则表
CREATE TABLE content_moderation_rules (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type SMALLINT NOT NULL, -- 1:关键词过滤, 2:图片审核, 3:音频审核, 4:视频审核, 5:链接检查
    content_type SMALLINT, -- 内容类型：1:文本, 2:图片, 3:音频, 4:视频, 5:文件
    rule_config JSONB NOT NULL, -- 规则配置
    action SMALLINT NOT NULL, -- 1:警告, 2:删除, 3:封禁, 4:人工审核
    severity SMALLINT DEFAULT 2, -- 1:轻微, 2:中等, 3:严重, 4:极严重
    is_active BOOLEAN DEFAULT TRUE,
    created_by BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_content_moderation_rules_type (type),
    INDEX idx_content_moderation_rules_content_type (content_type),
    INDEX idx_content_moderation_rules_action (action),
    INDEX idx_content_moderation_rules_severity (severity),
    INDEX idx_content_moderation_rules_is_active (is_active),
    INDEX idx_content_moderation_rules_created_by (created_by)
);

-- 内容审核记录表
CREATE TABLE content_moderation_records (
    id BIGSERIAL PRIMARY KEY,
    rule_id BIGINT,
    content_type SMALLINT NOT NULL, -- 1:消息, 2:用户资料, 3:群组信息, 4:媒体文件
    content_id VARCHAR(100) NOT NULL, -- 内容ID
    user_id BIGINT NOT NULL, -- 内容创建者
    violation_type VARCHAR(100), -- 违规类型
    violation_content TEXT, -- 违规内容
    confidence DECIMAL(5,4), -- 置信度
    action_taken SMALLINT, -- 采取的行动
    status SMALLINT DEFAULT 1, -- 1:待处理, 2:已处理, 3:误报, 4:申诉中
    reviewer_id BIGINT, -- 审核员ID
    review_notes TEXT, -- 审核备注
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (rule_id) REFERENCES content_moderation_rules(id) ON DELETE SET NULL,
    INDEX idx_content_moderation_records_rule_id (rule_id),
    INDEX idx_content_moderation_records_content_type (content_type),
    INDEX idx_content_moderation_records_content_id (content_id),
    INDEX idx_content_moderation_records_user_id (user_id),
    INDEX idx_content_moderation_records_violation_type (violation_type),
    INDEX idx_content_moderation_records_status (status),
    INDEX idx_content_moderation_records_reviewer_id (reviewer_id),
    INDEX idx_content_moderation_records_created_at (created_at)
);

-- 用户举报表
CREATE TABLE user_reports (
    id BIGSERIAL PRIMARY KEY,
    reporter_id BIGINT NOT NULL, -- 举报人
    reported_user_id BIGINT, -- 被举报用户
    reported_content_type SMALLINT NOT NULL, -- 1:用户, 2:消息, 3:群组, 4:频道, 5:媒体文件
    reported_content_id VARCHAR(100), -- 被举报内容ID
    report_type SMALLINT NOT NULL, -- 1:垃圾信息, 2:骚扰, 3:不当内容, 4:侵权, 5:其他
    reason TEXT NOT NULL, -- 举报原因
    evidence JSONB, -- 举报证据（截图等）
    status SMALLINT DEFAULT 1, -- 1:待处理, 2:处理中, 3:已处理, 4:已驳回
    priority SMALLINT DEFAULT 2, -- 1:低, 2:普通, 3:高, 4:紧急
    assigned_to BIGINT, -- 分配给的管理员
    resolution TEXT, -- 处理结果
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_reports_reporter_id (reporter_id),
    INDEX idx_user_reports_reported_user_id (reported_user_id),
    INDEX idx_user_reports_reported_content_type (reported_content_type),
    INDEX idx_user_reports_reported_content_id (reported_content_id),
    INDEX idx_user_reports_report_type (report_type),
    INDEX idx_user_reports_status (status),
    INDEX idx_user_reports_priority (priority),
    INDEX idx_user_reports_assigned_to (assigned_to),
    INDEX idx_user_reports_created_at (created_at)
);

-- 系统统计表
CREATE TABLE system_stats (
    id BIGSERIAL PRIMARY KEY,
    date DATE NOT NULL,
    
    -- 用户统计
    total_users BIGINT DEFAULT 0,
    active_users BIGINT DEFAULT 0, -- 日活用户
    new_users BIGINT DEFAULT 0, -- 新增用户
    deleted_users BIGINT DEFAULT 0, -- 删除用户
    
    -- 消息统计
    total_messages BIGINT DEFAULT 0,
    text_messages BIGINT DEFAULT 0,
    image_messages BIGINT DEFAULT 0,
    audio_messages BIGINT DEFAULT 0,
    video_messages BIGINT DEFAULT 0,
    file_messages BIGINT DEFAULT 0,
    
    -- 群组统计
    total_groups BIGINT DEFAULT 0,
    new_groups BIGINT DEFAULT 0,
    active_groups BIGINT DEFAULT 0,
    
    -- 频道统计
    total_channels BIGINT DEFAULT 0,
    new_channels BIGINT DEFAULT 0,
    active_channels BIGINT DEFAULT 0,
    
    -- 存储统计
    total_storage_used BIGINT DEFAULT 0, -- 字节
    storage_cost DECIMAL(10,2) DEFAULT 0,
    bandwidth_cost DECIMAL(10,2) DEFAULT 0,
    
    -- 审核统计
    moderation_actions BIGINT DEFAULT 0,
    user_reports BIGINT DEFAULT 0,
    banned_users BIGINT DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_system_stats_date (date),
    INDEX idx_system_stats_date (date)
);

-- 系统维护记录表
CREATE TABLE maintenance_records (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    type SMALLINT NOT NULL, -- 1:计划维护, 2:紧急维护, 3:功能更新, 4:安全更新
    status SMALLINT DEFAULT 1, -- 1:计划中, 2:进行中, 3:已完成, 4:已取消
    affected_services TEXT[], -- 受影响的服务
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    actual_start_time TIMESTAMP WITH TIME ZONE,
    actual_end_time TIMESTAMP WITH TIME ZONE,
    notify_users BOOLEAN DEFAULT TRUE,
    notification_sent BOOLEAN DEFAULT FALSE,
    created_by BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_maintenance_records_type (type),
    INDEX idx_maintenance_records_status (status),
    INDEX idx_maintenance_records_start_time (start_time),
    INDEX idx_maintenance_records_end_time (end_time),
    INDEX idx_maintenance_records_created_by (created_by)
);

-- 创建更新时间触发器
CREATE TRIGGER update_admins_updated_at BEFORE UPDATE ON admins
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configs_updated_at BEFORE UPDATE ON system_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_notifications_updated_at BEFORE UPDATE ON system_notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_moderation_rules_updated_at BEFORE UPDATE ON content_moderation_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_reports_updated_at BEFORE UPDATE ON user_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_stats_updated_at BEFORE UPDATE ON system_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_maintenance_records_updated_at BEFORE UPDATE ON maintenance_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, config_type, category, description, is_public) VALUES
('system.name', 'ZLIM', 1, 'system', '系统名称', true),
('system.version', '1.0.0', 1, 'system', '系统版本', true),
('system.maintenance_mode', 'false', 3, 'system', '维护模式', true),
('user.max_friends', '5000', 2, 'user', '最大好友数量', false),
('user.max_groups', '100', 2, 'user', '最大群组数量', false),
('message.max_length', '5000', 2, 'message', '消息最大长度', false),
('file.max_size', '104857600', 2, 'file', '文件最大大小（字节）', false),
('file.allowed_types', '["image/jpeg","image/png","image/gif","video/mp4","audio/mp3"]', 4, 'file', '允许的文件类型', false);

-- 插入默认管理员
INSERT INTO admins (user_id, username, email, role, permissions) VALUES
(1, 'admin', '<EMAIL>', 1, ARRAY['*']), -- 超级管理员，拥有所有权限
(2, 'system', '<EMAIL>', 2, ARRAY['system:read', 'user:read', 'message:read']);
