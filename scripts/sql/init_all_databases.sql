-- ========================================
-- 初始化所有数据库脚本
-- ========================================

-- 设置时区
SET timezone = 'UTC';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- 执行用户服务数据库初始化
\i 01_user_tables.sql

-- 执行消息服务数据库初始化
\i 02_message_tables.sql

-- 执行社交服务数据库初始化
\i 03_social_tables.sql

-- 执行媒体服务数据库初始化
\i 04_media_tables.sql

-- 执行管理服务数据库初始化
\i 05_admin_tables.sql

-- 创建跨数据库视图和函数（如果需要）

-- 创建全局统计视图
CREATE OR REPLACE VIEW global_stats AS
SELECT 
    'users' as metric_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 1 THEN 1 END) as active_count,
    COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END) as today_count
FROM zlim_user.users
WHERE deleted_at IS NULL

UNION ALL

SELECT 
    'messages' as metric_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status IN (2,3,4) THEN 1 END) as active_count,
    COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END) as today_count
FROM zlim_message.messages
WHERE is_deleted = false

UNION ALL

SELECT 
    'groups' as metric_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_count,
    COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END) as today_count
FROM zlim_social.groups
WHERE deleted_at IS NULL

UNION ALL

SELECT 
    'media_files' as metric_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 3 THEN 1 END) as active_count,
    COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END) as today_count
FROM zlim_media.media_files
WHERE deleted_at IS NULL;

-- 创建数据库连接测试函数
CREATE OR REPLACE FUNCTION test_database_connections()
RETURNS TABLE(database_name text, status text, message text) AS $$
BEGIN
    -- 测试用户数据库
    BEGIN
        PERFORM 1 FROM zlim_user.users LIMIT 1;
        RETURN QUERY SELECT 'zlim_user'::text, 'OK'::text, 'Connection successful'::text;
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT 'zlim_user'::text, 'ERROR'::text, SQLERRM::text;
    END;
    
    -- 测试消息数据库
    BEGIN
        PERFORM 1 FROM zlim_message.conversations LIMIT 1;
        RETURN QUERY SELECT 'zlim_message'::text, 'OK'::text, 'Connection successful'::text;
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT 'zlim_message'::text, 'ERROR'::text, SQLERRM::text;
    END;
    
    -- 测试社交数据库
    BEGIN
        PERFORM 1 FROM zlim_social.groups LIMIT 1;
        RETURN QUERY SELECT 'zlim_social'::text, 'OK'::text, 'Connection successful'::text;
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT 'zlim_social'::text, 'ERROR'::text, SQLERRM::text;
    END;
    
    -- 测试媒体数据库
    BEGIN
        PERFORM 1 FROM zlim_media.media_files LIMIT 1;
        RETURN QUERY SELECT 'zlim_media'::text, 'OK'::text, 'Connection successful'::text;
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT 'zlim_media'::text, 'ERROR'::text, SQLERRM::text;
    END;
    
    -- 测试管理数据库
    BEGIN
        PERFORM 1 FROM zlim_admin.admins LIMIT 1;
        RETURN QUERY SELECT 'zlim_admin'::text, 'OK'::text, 'Connection successful'::text;
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT 'zlim_admin'::text, 'ERROR'::text, SQLERRM::text;
    END;
END;
$$ LANGUAGE plpgsql;

-- 创建数据库健康检查函数
CREATE OR REPLACE FUNCTION database_health_check()
RETURNS TABLE(
    check_name text, 
    status text, 
    value bigint, 
    threshold bigint, 
    message text
) AS $$
BEGIN
    -- 检查用户数量
    RETURN QUERY 
    SELECT 
        'active_users'::text,
        CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'WARNING' END::text,
        COUNT(*)::bigint,
        1::bigint,
        CASE WHEN COUNT(*) > 0 THEN 'Users exist' ELSE 'No active users' END::text
    FROM zlim_user.users 
    WHERE status = 1 AND deleted_at IS NULL;
    
    -- 检查今日消息数量
    RETURN QUERY 
    SELECT 
        'daily_messages'::text,
        'OK'::text,
        COUNT(*)::bigint,
        0::bigint,
        'Messages sent today'::text
    FROM zlim_message.messages 
    WHERE created_at >= CURRENT_DATE;
    
    -- 检查存储使用情况
    RETURN QUERY 
    SELECT 
        'storage_usage'::text,
        CASE WHEN COALESCE(SUM(file_size), 0) < ********** THEN 'OK' ELSE 'WARNING' END::text,
        COALESCE(SUM(file_size), 0)::bigint,
        **********::bigint, -- 1GB threshold
        'Total storage used (bytes)'::text
    FROM zlim_media.media_files 
    WHERE status = 3 AND deleted_at IS NULL;
    
    -- 检查待处理的举报数量
    RETURN QUERY 
    SELECT 
        'pending_reports'::text,
        CASE WHEN COUNT(*) < 10 THEN 'OK' ELSE 'WARNING' END::text,
        COUNT(*)::bigint,
        10::bigint,
        'Pending user reports'::text
    FROM zlim_admin.user_reports 
    WHERE status = 1;
END;
$$ LANGUAGE plpgsql;

-- 创建清理过期数据的函数
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS void AS $$
BEGIN
    -- 清理过期的好友请求
    UPDATE zlim_social.friend_requests 
    SET status = 4 
    WHERE status = 1 AND expires_at < CURRENT_TIMESTAMP;
    
    -- 清理过期的群邀请
    UPDATE zlim_social.group_invitations 
    SET status = 4 
    WHERE status = 1 AND expires_at < CURRENT_TIMESTAMP;
    
    -- 清理过期的群加入申请
    UPDATE zlim_social.group_join_requests 
    SET status = 4 
    WHERE status = 1 AND expires_at < CURRENT_TIMESTAMP;
    
    -- 清理过期的用户会话
    UPDATE zlim_user.user_sessions 
    SET is_active = false 
    WHERE is_active = true AND expires_at < CURRENT_TIMESTAMP;
    
    -- 清理过期的上传会话
    DELETE FROM zlim_media.upload_sessions 
    WHERE expires_at < CURRENT_TIMESTAMP AND status IN (1, 4, 5);
    
    -- 清理过期的媒体文件
    UPDATE zlim_media.media_files 
    SET status = 5, deleted_at = CURRENT_TIMESTAMP
    WHERE expires_at < CURRENT_TIMESTAMP AND status != 5;
    
    RAISE NOTICE 'Expired data cleanup completed';
END;
$$ LANGUAGE plpgsql;

-- 创建数据库备份函数
CREATE OR REPLACE FUNCTION create_backup_info()
RETURNS TABLE(
    database_name text,
    table_count bigint,
    total_size text,
    last_backup timestamp with time zone
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'zlim_user'::text,
        (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_catalog = 'zlim_user')::bigint,
        pg_size_pretty(pg_database_size('zlim_user'))::text,
        CURRENT_TIMESTAMP;
        
    RETURN QUERY
    SELECT 
        'zlim_message'::text,
        (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_catalog = 'zlim_message')::bigint,
        pg_size_pretty(pg_database_size('zlim_message'))::text,
        CURRENT_TIMESTAMP;
        
    RETURN QUERY
    SELECT 
        'zlim_social'::text,
        (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_catalog = 'zlim_social')::bigint,
        pg_size_pretty(pg_database_size('zlim_social'))::text,
        CURRENT_TIMESTAMP;
        
    RETURN QUERY
    SELECT 
        'zlim_media'::text,
        (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_catalog = 'zlim_media')::bigint,
        pg_size_pretty(pg_database_size('zlim_media'))::text,
        CURRENT_TIMESTAMP;
        
    RETURN QUERY
    SELECT 
        'zlim_admin'::text,
        (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_catalog = 'zlim_admin')::bigint,
        pg_size_pretty(pg_database_size('zlim_admin'))::text,
        CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '========================================';
    RAISE NOTICE 'ZLIM Database Initialization Complete!';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Databases created:';
    RAISE NOTICE '  - zlim_user (User management)';
    RAISE NOTICE '  - zlim_message (Message system)';
    RAISE NOTICE '  - zlim_social (Social features)';
    RAISE NOTICE '  - zlim_media (Media files)';
    RAISE NOTICE '  - zlim_admin (Administration)';
    RAISE NOTICE '';
    RAISE NOTICE 'Default users created:';
    RAISE NOTICE '  - admin (Super Administrator)';
    RAISE NOTICE '  - system (System User)';
    RAISE NOTICE '';
    RAISE NOTICE 'Utility functions available:';
    RAISE NOTICE '  - test_database_connections()';
    RAISE NOTICE '  - database_health_check()';
    RAISE NOTICE '  - cleanup_expired_data()';
    RAISE NOTICE '  - create_backup_info()';
    RAISE NOTICE '';
    RAISE NOTICE 'Run "SELECT * FROM test_database_connections();" to verify setup.';
    RAISE NOTICE '========================================';
END $$;
