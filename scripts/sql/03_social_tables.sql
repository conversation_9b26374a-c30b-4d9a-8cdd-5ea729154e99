-- ========================================
-- 社交服务数据库表结构
-- ========================================

-- 创建社交数据库
CREATE DATABASE IF NOT EXISTS zlim_social;
USE zlim_social;

-- 好友关系表
CREATE TABLE friendships (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    friend_id BIGINT NOT NULL,
    nickname VARCHAR(100), -- 好友备注
    tags TEXT[], -- 好友标签
    is_favorite BOOLEAN DEFAULT FALSE, -- 是否特别关注
    is_blocked BOOLEAN DEFAULT FALSE, -- 是否拉黑
    blocked_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_friendships_user_friend (user_id, friend_id),
    INDEX idx_friendships_user_id (user_id),
    INDEX idx_friendships_friend_id (friend_id),
    INDEX idx_friendships_is_favorite (is_favorite),
    INDEX idx_friendships_is_blocked (is_blocked),
    INDEX idx_friendships_created_at (created_at),
    
    -- 确保不能添加自己为好友
    CHECK (user_id != friend_id)
);

-- 好友请求表
CREATE TABLE friend_requests (
    id BIGSERIAL PRIMARY KEY,
    from_user_id BIGINT NOT NULL,
    to_user_id BIGINT NOT NULL,
    message TEXT,
    source VARCHAR(50), -- 来源：search, recommendation, qr_code, etc.
    status SMALLINT DEFAULT 1, -- 1:待处理, 2:已接受, 3:已拒绝, 4:已过期
    processed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '7 days'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_friend_requests_from_to (from_user_id, to_user_id),
    INDEX idx_friend_requests_from_user_id (from_user_id),
    INDEX idx_friend_requests_to_user_id (to_user_id),
    INDEX idx_friend_requests_status (status),
    INDEX idx_friend_requests_expires_at (expires_at),
    INDEX idx_friend_requests_created_at (created_at),
    
    -- 确保不能向自己发送好友请求
    CHECK (from_user_id != to_user_id)
);

-- 黑名单表
CREATE TABLE blocked_users (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    blocked_user_id BIGINT NOT NULL,
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_blocked_users_user_blocked (user_id, blocked_user_id),
    INDEX idx_blocked_users_user_id (user_id),
    INDEX idx_blocked_users_blocked_user_id (blocked_user_id),
    INDEX idx_blocked_users_created_at (created_at),
    
    -- 确保不能拉黑自己
    CHECK (user_id != blocked_user_id)
);

-- 群组表
CREATE TABLE groups (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    avatar VARCHAR(500),
    type SMALLINT DEFAULT 1, -- 1:私有群, 2:公开群, 3:官方群
    join_mode SMALLINT DEFAULT 1, -- 1:自由加入, 2:需要审批, 3:仅邀请
    max_members INT DEFAULT 500,
    member_count INT DEFAULT 0,
    owner_id BIGINT NOT NULL,
    invite_code VARCHAR(32) UNIQUE,
    invite_code_expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    settings JSONB, -- 群组设置
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    INDEX idx_groups_owner_id (owner_id),
    INDEX idx_groups_type (type),
    INDEX idx_groups_join_mode (join_mode),
    INDEX idx_groups_invite_code (invite_code),
    INDEX idx_groups_is_active (is_active),
    INDEX idx_groups_created_at (created_at),
    INDEX idx_groups_deleted_at (deleted_at)
);

-- 群成员表
CREATE TABLE group_members (
    id BIGSERIAL PRIMARY KEY,
    group_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role SMALLINT DEFAULT 1, -- 1:普通成员, 2:管理员, 3:群主
    nickname VARCHAR(100), -- 群昵称
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    invited_by BIGINT, -- 邀请人
    is_muted BOOLEAN DEFAULT FALSE,
    mute_until TIMESTAMP WITH TIME ZONE,
    left_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE,
    UNIQUE KEY uk_group_members_group_user (group_id, user_id),
    INDEX idx_group_members_group_id (group_id),
    INDEX idx_group_members_user_id (user_id),
    INDEX idx_group_members_role (role),
    INDEX idx_group_members_joined_at (joined_at),
    INDEX idx_group_members_invited_by (invited_by),
    INDEX idx_group_members_left_at (left_at)
);

-- 群邀请表
CREATE TABLE group_invitations (
    id BIGSERIAL PRIMARY KEY,
    group_id BIGINT NOT NULL,
    inviter_id BIGINT NOT NULL,
    invitee_id BIGINT NOT NULL,
    message TEXT,
    status SMALLINT DEFAULT 1, -- 1:待处理, 2:已接受, 3:已拒绝, 4:已过期
    processed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '7 days'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE,
    UNIQUE KEY uk_group_invitations_group_invitee (group_id, invitee_id),
    INDEX idx_group_invitations_group_id (group_id),
    INDEX idx_group_invitations_inviter_id (inviter_id),
    INDEX idx_group_invitations_invitee_id (invitee_id),
    INDEX idx_group_invitations_status (status),
    INDEX idx_group_invitations_expires_at (expires_at),
    INDEX idx_group_invitations_created_at (created_at)
);

-- 群加入申请表
CREATE TABLE group_join_requests (
    id BIGSERIAL PRIMARY KEY,
    group_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    message TEXT,
    status SMALLINT DEFAULT 1, -- 1:待处理, 2:已通过, 3:已拒绝, 4:已过期
    processed_by BIGINT, -- 处理人
    processed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '7 days'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE,
    UNIQUE KEY uk_group_join_requests_group_user (group_id, user_id),
    INDEX idx_group_join_requests_group_id (group_id),
    INDEX idx_group_join_requests_user_id (user_id),
    INDEX idx_group_join_requests_status (status),
    INDEX idx_group_join_requests_processed_by (processed_by),
    INDEX idx_group_join_requests_expires_at (expires_at),
    INDEX idx_group_join_requests_created_at (created_at)
);

-- 频道表
CREATE TABLE channels (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    avatar VARCHAR(500),
    type SMALLINT DEFAULT 1, -- 1:新闻频道, 2:公告频道, 3:讨论频道, 4:娱乐频道
    is_public BOOLEAN DEFAULT TRUE,
    owner_id BIGINT NOT NULL,
    subscriber_count BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    settings JSONB, -- 频道设置
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    INDEX idx_channels_owner_id (owner_id),
    INDEX idx_channels_type (type),
    INDEX idx_channels_is_public (is_public),
    INDEX idx_channels_is_active (is_active),
    INDEX idx_channels_created_at (created_at),
    INDEX idx_channels_deleted_at (deleted_at)
);

-- 频道订阅表
CREATE TABLE channel_subscriptions (
    id BIGSERIAL PRIMARY KEY,
    channel_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    notifications_enabled BOOLEAN DEFAULT TRUE,
    subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (channel_id) REFERENCES channels(id) ON DELETE CASCADE,
    UNIQUE KEY uk_channel_subscriptions_channel_user (channel_id, user_id),
    INDEX idx_channel_subscriptions_channel_id (channel_id),
    INDEX idx_channel_subscriptions_user_id (user_id),
    INDEX idx_channel_subscriptions_subscribed_at (subscribed_at),
    INDEX idx_channel_subscriptions_unsubscribed_at (unsubscribed_at)
);

-- 社交统计表
CREATE TABLE social_stats (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    friends_count BIGINT DEFAULT 0,
    groups_count BIGINT DEFAULT 0,
    channels_count BIGINT DEFAULT 0,
    friend_requests_sent BIGINT DEFAULT 0,
    friend_requests_received BIGINT DEFAULT 0,
    group_invitations_sent BIGINT DEFAULT 0,
    group_invitations_received BIGINT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_social_stats_user_id (user_id),
    INDEX idx_social_stats_friends_count (friends_count),
    INDEX idx_social_stats_groups_count (groups_count)
);

-- 社交活动日志表
CREATE TABLE social_activity_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    target_user_id BIGINT,
    target_group_id BIGINT,
    target_channel_id BIGINT,
    activity_type SMALLINT NOT NULL, -- 1:添加好友, 2:删除好友, 3:加入群组, 4:离开群组, 5:订阅频道, 6:取消订阅频道
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_social_activity_logs_user_id (user_id),
    INDEX idx_social_activity_logs_target_user_id (target_user_id),
    INDEX idx_social_activity_logs_target_group_id (target_group_id),
    INDEX idx_social_activity_logs_target_channel_id (target_channel_id),
    INDEX idx_social_activity_logs_activity_type (activity_type),
    INDEX idx_social_activity_logs_created_at (created_at)
);

-- 创建更新时间触发器
CREATE TRIGGER update_friendships_updated_at BEFORE UPDATE ON friendships
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_friend_requests_updated_at BEFORE UPDATE ON friend_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_groups_updated_at BEFORE UPDATE ON groups
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_group_invitations_updated_at BEFORE UPDATE ON group_invitations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_group_join_requests_updated_at BEFORE UPDATE ON group_join_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_channels_updated_at BEFORE UPDATE ON channels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_social_stats_updated_at BEFORE UPDATE ON social_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建群成员数量更新触发器函数
CREATE OR REPLACE FUNCTION update_group_member_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE groups SET member_count = member_count + 1 WHERE id = NEW.group_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE groups SET member_count = member_count - 1 WHERE id = OLD.group_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 为群成员表添加触发器
CREATE TRIGGER update_group_member_count_trigger
    AFTER INSERT OR DELETE ON group_members
    FOR EACH ROW EXECUTE FUNCTION update_group_member_count();

-- 创建频道订阅数量更新触发器函数
CREATE OR REPLACE FUNCTION update_channel_subscriber_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE channels SET subscriber_count = subscriber_count + 1 WHERE id = NEW.channel_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE channels SET subscriber_count = subscriber_count - 1 WHERE id = OLD.channel_id;
    ELSIF TG_OP = 'UPDATE' THEN
        -- 处理取消订阅的情况
        IF OLD.unsubscribed_at IS NULL AND NEW.unsubscribed_at IS NOT NULL THEN
            UPDATE channels SET subscriber_count = subscriber_count - 1 WHERE id = NEW.channel_id;
        ELSIF OLD.unsubscribed_at IS NOT NULL AND NEW.unsubscribed_at IS NULL THEN
            UPDATE channels SET subscriber_count = subscriber_count + 1 WHERE id = NEW.channel_id;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 为频道订阅表添加触发器
CREATE TRIGGER update_channel_subscriber_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON channel_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_channel_subscriber_count();

-- 创建生成邀请码函数
CREATE OR REPLACE FUNCTION generate_invite_code()
RETURNS TEXT AS $$
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    result TEXT := '';
    i INTEGER;
BEGIN
    FOR i IN 1..8 LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 为群组表添加邀请码生成触发器
CREATE OR REPLACE FUNCTION set_group_invite_code()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.invite_code IS NULL THEN
        NEW.invite_code := generate_invite_code();
        NEW.invite_code_expires_at := CURRENT_TIMESTAMP + INTERVAL '30 days';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_group_invite_code_trigger
    BEFORE INSERT ON groups
    FOR EACH ROW EXECUTE FUNCTION set_group_invite_code();
