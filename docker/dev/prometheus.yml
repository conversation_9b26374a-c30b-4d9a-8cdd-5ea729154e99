global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Spring Boot 应用监控
  - job_name: 'spring-boot-apps'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: 
        - 'host.docker.internal:8080'  # gateway-service
        - 'host.docker.internal:8081'  # user-service
        - 'host.docker.internal:8082'  # auth-service
        - 'host.docker.internal:8083'  # message-service
        - 'host.docker.internal:8084'  # social-service
        - 'host.docker.internal:8085'  # media-service
        - 'host.docker.internal:8086'  # push-service
        - 'host.docker.internal:8087'  # admin-service
        - 'host.docker.internal:8088'  # notification-service

  # Redis 监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  # PostgreSQL 监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  # RocketMQ 监控
  - job_name: 'rocketmq'
    static_configs:
      - targets: ['rocketmq-nameserver:9876', 'rocketmq-broker:10911']

  # Node Exporter (如果需要系统监控)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
