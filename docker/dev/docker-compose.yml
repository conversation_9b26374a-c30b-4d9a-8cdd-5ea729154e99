version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:16-alpine
    container_name: zlim-postgres
    environment:
      POSTGRES_DB: zlim
      POSTGRES_USER: zlim
      POSTGRES_PASSWORD: zlim123
      POSTGRES_MULTIPLE_DATABASES: zlim_user,zlim_message,zlim_social,zlim_media,zlim_admin
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - zlim-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U zlim"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: zlim-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - zlim-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Nacos 服务注册发现
  nacos:
    image: nacos/nacos-server:v2.3.2
    container_name: zlim-nacos
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: nacos-mysql
      MYSQL_SERVICE_DB_NAME: nacos_devtest
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_USER: nacos
      MYSQL_SERVICE_PASSWORD: nacos
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=Asia/Shanghai
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos_data:/home/<USER>/data
    networks:
      - zlim-network
    depends_on:
      nacos-mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nacos MySQL
  nacos-mysql:
    image: mysql:8.0
    container_name: zlim-nacos-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: nacos_devtest
      MYSQL_USER: nacos
      MYSQL_PASSWORD: nacos
    volumes:
      - nacos_mysql_data:/var/lib/mysql
    networks:
      - zlim-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 10

  # RocketMQ NameServer
  rocketmq-nameserver:
    image: apache/rocketmq:5.3.0
    container_name: zlim-rocketmq-nameserver
    ports:
      - "9876:9876"
    environment:
      JAVA_OPT_EXT: "-Duser.home=/home/<USER>"
    command: ["sh", "mqnamesrv"]
    volumes:
      - rocketmq_nameserver_data:/home/<USER>/logs
    networks:
      - zlim-network

  # RocketMQ Broker
  rocketmq-broker:
    image: apache/rocketmq:5.3.0
    container_name: zlim-rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
    environment:
      JAVA_OPT_EXT: "-Duser.home=/home/<USER>"
      NAMESRV_ADDR: "rocketmq-nameserver:9876"
    command: ["sh", "mqbroker", "-c", "/home/<USER>/rocketmq-5.3.0/conf/broker.conf"]
    volumes:
      - rocketmq_broker_data:/home/<USER>/logs
      - rocketmq_broker_store:/home/<USER>/store
      - ./broker.conf:/home/<USER>/rocketmq-5.3.0/conf/broker.conf
    networks:
      - zlim-network
    depends_on:
      - rocketmq-nameserver

  # RocketMQ Dashboard
  rocketmq-dashboard:
    image: apacherocketmq/rocketmq-dashboard:1.0.0
    container_name: zlim-rocketmq-dashboard
    ports:
      - "8180:8080"
    environment:
      JAVA_OPTS: "-Drocketmq.namesrv.addr=rocketmq-nameserver:9876"
    networks:
      - zlim-network
    depends_on:
      - rocketmq-nameserver

  # MinIO 对象存储
  minio:
    image: minio/minio:RELEASE.2024-07-04T14-25-45Z
    container_name: zlim-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - zlim-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:v2.53.0
    container_name: zlim-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - zlim-network

  # Grafana 监控面板
  grafana:
    image: grafana/grafana:11.1.0
    container_name: zlim-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - zlim-network

  # Jaeger 链路追踪
  jaeger:
    image: jaegertracing/all-in-one:1.58
    container_name: zlim-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      COLLECTOR_OTLP_ENABLED: true
    networks:
      - zlim-network

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.14.3
    container_name: zlim-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - zlim-network

  # Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.14.3
    container_name: zlim-kibana
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    networks:
      - zlim-network
    depends_on:
      - elasticsearch

  # XXL-Job 任务调度
  xxl-job:
    image: xuxueli/xxl-job-admin:2.4.1
    container_name: zlim-xxl-job
    ports:
      - "8280:8080"
    environment:
      PARAMS: "--spring.datasource.url=******************************************************************************************************************************* --spring.datasource.username=root --spring.datasource.password=root"
    networks:
      - zlim-network
    depends_on:
      xxl-job-mysql:
        condition: service_healthy

  # XXL-Job MySQL
  xxl-job-mysql:
    image: mysql:8.0
    container_name: zlim-xxl-job-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: xxl_job
    volumes:
      - xxl_job_mysql_data:/var/lib/mysql
      - ./xxl-job-init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - zlim-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 10

volumes:
  postgres_data:
  redis_data:
  nacos_data:
  nacos_mysql_data:
  rocketmq_nameserver_data:
  rocketmq_broker_data:
  rocketmq_broker_store:
  minio_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:
  xxl_job_mysql_data:

networks:
  zlim-network:
    driver: bridge
