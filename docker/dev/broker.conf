# RocketMQ Broker 配置
brokerClusterName = DefaultCluster
brokerName = broker-a
brokerId = 0
deleteWhen = 04
fileReservedTime = 48
brokerRole = ASYNC_MASTER
flushDiskType = ASYNC_FLUSH

# 网络配置
brokerIP1 = 0.0.0.0
listenPort = 10911
namesrvAddr = rocketmq-nameserver:9876

# 存储配置
storePathRootDir = /home/<USER>/store
storePathCommitLog = /home/<USER>/store/commitlog
storePathConsumeQueue = /home/<USER>/store/consumequeue
storePathIndex = /home/<USER>/store/index

# 性能配置
sendMessageThreadPoolNums = 128
pullMessageThreadPoolNums = 128
queryMessageThreadPoolNums = 8
adminBrokerThreadPoolNums = 16
clientManagerThreadPoolNums = 32
consumerManagerThreadPoolNums = 32

# 消息配置
maxMessageSize = 4194304
transientStorePoolEnable = false
transferMsgByHeap = true
maxTransferBytesOnMessageInMemory = 262144
maxTransferCountOnMessageInMemory = 32
maxTransferBytesOnMessageInDisk = 65536
maxTransferCountOnMessageInDisk = 8

# 自动创建Topic
autoCreateTopicEnable = true
autoCreateSubscriptionGroup = true

# 消息轨迹
traceTopicEnable = true
msgTraceTopicName = RMQ_SYS_TRACE_TOPIC
