rootProject.name = "zlim"

// 微服务模块
include(
    // 网关服务
    ":gateway-service",
    
    // 业务服务
    ":user-service",
    ":auth-service", 
    ":message-service",
    ":social-service",
    ":media-service",
    ":push-service",
    ":admin-service",
    ":notification-service",
    
    // 公共模块
    ":common",
    ":proto"
)

// 设置项目目录
project(":gateway-service").projectDir = file("services/gateway-service")
project(":user-service").projectDir = file("services/user-service")
project(":auth-service").projectDir = file("services/auth-service")
project(":message-service").projectDir = file("services/message-service")
project(":social-service").projectDir = file("services/social-service")
project(":media-service").projectDir = file("services/media-service")
project(":push-service").projectDir = file("services/push-service")
project(":admin-service").projectDir = file("services/admin-service")
project(":notification-service").projectDir = file("services/notification-service")
project(":common").projectDir = file("common")
project(":proto").projectDir = file("proto")

// 插件管理
pluginManagement {
    repositories {
        gradlePluginPortal()
        mavenCentral()
        maven("https://repo.spring.io/milestone")
    }
}

// 依赖解析管理
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        mavenCentral()
        maven("https://repo.spring.io/milestone")
        maven("https://packages.confluent.io/maven/")
    }
}
